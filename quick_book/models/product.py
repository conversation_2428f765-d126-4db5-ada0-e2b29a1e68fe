from core import models


class ProductQuerySet(models.QuerySet):
    def active_products(self, **kwargs):
        return self.filter(is_active=True, **kwargs)

    def get_active_product(self,  **kwargs):
        return self.get(is_active=True, **kwargs)


class Product(models.AbstractBaseModel):
    name = models.CharField(max_length=100)
    quickbooks_id = models.CharField(max_length=50)
    vendor = models.ForeignKey("quick_book.Vendor", null=True, blank=True, on_delete=models.SET_NULL)
    quantity = models.IntegerField(default=0)
    saleor_id = models.CharField(max_length=100, null=True, blank=True)
    category = models.ForeignKey("quick_book.Category", null=True, blank=True, on_delete=models.SET_NULL,
                                 related_name="products")
    sku = models.CharField(max_length=50, null=True, blank=True)
    saleor_variant_id = models.CharField(max_length=100, null=True, blank=True)
    cost_price = models.FloatField(null=True, blank=True)
    sales_price = models.FloatField(null=True, blank=True)
    site = models.ForeignKey(models.Site, related_name="quick_book_products", null=True, blank=True,
                             on_delete=models.PROTECT)
    is_bundle = models.BooleanField(default=False)
    is_manufacture = models.BooleanField(default=False)

    objects = ProductQuerySet.as_manager()

    def __str__(self):
        return "{}| {}| {}".format(self.name, self.quickbooks_id, self.site)

    @property
    def manufacture_inventory_product(self):
        if not self.is_manufacture:
            raise ValueError('Product is not manufacture')

        manufacture_inventory_product_sku = f'p-{self.sku}'

        return Product.objects.get(
            is_manufacture=True,
            sku=manufacture_inventory_product_sku,
        )

    @property
    def manufacture_bundle_product(self):
        if not self.is_manufacture:
            raise ValueError('Product is not manufacture')

        manufacture_bundle_product_sku = self.sku[2:]

        return Product.objects.get(
            is_manufacture=True,
            sku=manufacture_bundle_product_sku,
        )

    def get_quickbooks_object(self, quickbooks_client):
        from quickbooks.objects.item import Item

        return Item.get(self.quickbooks_id, qb=quickbooks_client)

    def save(self, *args, **kwargs):
        if self.sku is not None:
            self.is_manufacture = self.sku.startswith('p-manu-') or self.sku.startswith('manu-')
            self.is_bundle = self.sku.startswith('manu-')

        super().save(*args, **kwargs)
