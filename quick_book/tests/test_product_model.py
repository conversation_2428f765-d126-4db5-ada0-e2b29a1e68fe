from unittest.mock import Mock, patch
from django.test import TestCase
from django.contrib.sites.models import Site
from django.core.exceptions import ObjectDoesNotExist

from quick_book.models import Product, Category


class ProductModelTest(TestCase):
    """Test cases for Product model properties and methods."""

    def setUp(self):
        """Set up test data."""
        # Create a test site
        self.site = Site.objects.create(domain="test.com", name="test")
        
        # Create a test category
        self.category = Category.objects.create(
            name="Test Category",
            quickbooks_id="test_qb_id",
            site=self.site
        )

    def test_manufacture_inventory_product_success(self):
        """Test manufacture_inventory_product property returns correct product."""
        # Create a manufacture product
        manufacture_product = Product.objects.create(
            name="Manufacture Product",
            quickbooks_id="manu_qb_id",
            sku="manu-test-123",
            is_manufacture=True,
            site=self.site,
            category=self.category
        )
        
        # Create the corresponding inventory product
        inventory_product = Product.objects.create(
            name="Inventory Product",
            quickbooks_id="inv_qb_id",
            sku="p-manu-test-123",
            is_manufacture=True,
            site=self.site,
            category=self.category
        )
        
        # Test the property
        result = manufacture_product.manufacture_inventory_product
        self.assertEqual(result, inventory_product)
        self.assertEqual(result.sku, "p-manu-test-123")

    def test_manufacture_inventory_product_not_manufacture_raises_error(self):
        """Test manufacture_inventory_product raises ValueError when product is not manufacture."""
        # Create a non-manufacture product
        regular_product = Product.objects.create(
            name="Regular Product",
            quickbooks_id="reg_qb_id",
            sku="regular-123",
            is_manufacture=False,
            site=self.site,
            category=self.category
        )
        
        # Test that ValueError is raised
        with self.assertRaises(ValueError) as context:
            _ = regular_product.manufacture_inventory_product
        
        self.assertEqual(str(context.exception), 'Product is not manufacture')

    def test_manufacture_inventory_product_not_found_raises_error(self):
        """Test manufacture_inventory_product raises DoesNotExist when inventory product not found."""
        # Create a manufacture product without corresponding inventory product
        manufacture_product = Product.objects.create(
            name="Manufacture Product",
            quickbooks_id="manu_qb_id",
            sku="manu-test-456",
            is_manufacture=True,
            site=self.site,
            category=self.category
        )
        
        # Test that DoesNotExist is raised
        with self.assertRaises(Product.DoesNotExist):
            _ = manufacture_product.manufacture_inventory_product

    def test_manufacture_bundle_product_success(self):
        """Test manufacture_bundle_product property returns correct product."""
        # Create a manufacture product with p- prefix
        manufacture_product = Product.objects.create(
            name="Manufacture Product",
            quickbooks_id="manu_qb_id",
            sku="p-manu-789",
            is_manufacture=True,
            site=self.site,
            category=self.category
        )
        
        # Create the corresponding bundle product
        bundle_product = Product.objects.create(
            name="Bundle Product",
            quickbooks_id="bundle_qb_id",
            sku="manu-789",
            is_manufacture=True,
            site=self.site,
            category=self.category
        )
        
        # Test the property
        result = manufacture_product.manufacture_bundle_product
        self.assertEqual(result, bundle_product)
        self.assertEqual(result.sku, "manu-789")

    def test_manufacture_bundle_product_not_manufacture_raises_error(self):
        """Test manufacture_bundle_product raises ValueError when product is not manufacture."""
        # Create a non-manufacture product
        regular_product = Product.objects.create(
            name="Regular Product",
            quickbooks_id="reg_qb_id",
            sku="p-regular-123",
            is_manufacture=False,
            site=self.site,
            category=self.category
        )
        
        # Test that ValueError is raised
        with self.assertRaises(ValueError) as context:
            _ = regular_product.manufacture_bundle_product
        
        self.assertEqual(str(context.exception), 'Product is not manufacture')

    def test_manufacture_bundle_product_not_found_raises_error(self):
        """Test manufacture_bundle_product raises DoesNotExist when bundle product not found."""
        # Create a manufacture product without corresponding bundle product
        manufacture_product = Product.objects.create(
            name="Manufacture Product",
            quickbooks_id="manu_qb_id",
            sku="p-manu-999",
            is_manufacture=True,
            site=self.site,
            category=self.category
        )
        
        # Test that DoesNotExist is raised
        with self.assertRaises(Product.DoesNotExist):
            _ = manufacture_product.manufacture_bundle_product

    @patch('quickbooks.objects.item.Item')
    def test_get_quickbooks_object_success(self, mock_item_class):
        """Test get_quickbooks_object method returns correct QuickBooks item."""
        # Create a product
        product = Product.objects.create(
            name="Test Product",
            quickbooks_id="test_qb_123",
            site=self.site,
            category=self.category
        )
        
        # Mock the QuickBooks client and Item
        mock_client = Mock()
        mock_item = Mock()
        mock_item_class.get.return_value = mock_item
        
        # Test the method
        result = product.get_quickbooks_object(mock_client)
        
        # Verify the correct calls were made
        mock_item_class.get.assert_called_once_with("test_qb_123", qb=mock_client)
        self.assertEqual(result, mock_item)

    def test_save_sets_is_manufacture_for_p_manu_prefix(self):
        """Test save method sets is_manufacture=True for SKUs starting with 'p-manu-'."""
        product = Product.objects.create(
            name="Test Product",
            quickbooks_id="test_qb_id",
            sku="p-manu-test-123",
            site=self.site,
            category=self.category
        )
        
        self.assertTrue(product.is_manufacture)
        self.assertFalse(product.is_bundle)

    def test_save_sets_is_manufacture_for_manu_prefix(self):
        """Test save method sets is_manufacture=True for SKUs starting with 'manu-'."""
        product = Product.objects.create(
            name="Test Product",
            quickbooks_id="test_qb_id",
            sku="manu-test-456",
            site=self.site,
            category=self.category
        )
        
        self.assertTrue(product.is_manufacture)
        self.assertTrue(product.is_bundle)

    def test_save_does_not_set_flags_for_other_prefixes(self):
        """Test save method does not set flags for SKUs with other prefixes."""
        product = Product.objects.create(
            name="Test Product",
            quickbooks_id="test_qb_id",
            sku="regular-test-789",
            site=self.site,
            category=self.category
        )
        
        self.assertFalse(product.is_manufacture)
        self.assertFalse(product.is_bundle)

    def test_save_handles_none_sku(self):
        """Test save method handles None SKU without errors."""
        product = Product.objects.create(
            name="Test Product",
            quickbooks_id="test_qb_id",
            sku=None,
            site=self.site,
            category=self.category
        )
        
        self.assertFalse(product.is_manufacture)
        self.assertFalse(product.is_bundle)

    def test_save_updates_existing_product_flags(self):
        """Test save method updates flags when SKU is changed."""
        # Create product with regular SKU
        product = Product.objects.create(
            name="Test Product",
            quickbooks_id="test_qb_id",
            sku="regular-123",
            site=self.site,
            category=self.category
        )
        
        self.assertFalse(product.is_manufacture)
        self.assertFalse(product.is_bundle)
        
        # Update SKU to manufacture prefix
        product.sku = "manu-test-123"
        product.save()
        
        self.assertTrue(product.is_manufacture)
        self.assertTrue(product.is_bundle)

    def test_save_preserves_manually_set_flags(self):
        """Test save method preserves manually set flags when appropriate."""
        # Create product with manufacture SKU but manually set flags to False
        product = Product(
            name="Test Product",
            quickbooks_id="test_qb_id",
            sku="regular-123",
            is_manufacture=True,  # Manually set
            is_bundle=True,       # Manually set
            site=self.site,
            category=self.category
        )
        product.save()
        
        # Since SKU doesn't match patterns, flags should be overridden by save logic
        self.assertFalse(product.is_manufacture)
        self.assertFalse(product.is_bundle)
