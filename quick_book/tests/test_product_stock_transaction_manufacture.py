from unittest.mock import Mock, patch

from django.contrib.sites.models import Site
from django.test import TransactionTestCase

from constants import ProductStockTransactionType, TransactionState, TransactionReason
from quick_book.models import Product, ProductStockTransaction, Category, ProductItem
from saleor.models import Order, OrderLine


class ProductStockTransactionManufactureTest(TransactionTestCase):
    """Test cases for ProductStockTransaction is_manufacture functionality."""

    def setUp(self):
        """Set up test data."""
        # Create a test site
        self.site = Site.objects.create(domain="test.com", name="test")

        # Create a test category
        self.category = Category.objects.create(
            name="Test Category",
            quickbooks_id="test_qb_id",
            site=self.site
        )

        # Create test order and order line
        self.order = Order.objects.create(
            saleor_id="test_order_123",
            saleor_user_email="<EMAIL>",
            saleor_response={'voucher': None},
        )

        self.order_line = OrderLine.objects.create(
            order=self.order,
            saleor_id="test_line_123",
            product_name="Test Product",
            quantity=2
        )

    def create_manufacture_product_setup(self):
        """Create a complete manufacture product setup with bundle and items."""
        # Create bundle product (manu- prefix)
        self.bundle_product = Product.objects.create(
            name="Bundle Product",
            quickbooks_id="bundle_qb_123",
            sku="manu-test-bundle",
            is_manufacture=True,
            is_bundle=True,
            site=self.site,
            category=self.category,
            quantity=0
        )

        # Create inventory product (p-manu- prefix)
        self.inventory_product = Product.objects.create(
            name="Inventory Product",
            quickbooks_id="inv_qb_123",
            sku="p-manu-test-bundle",
            is_manufacture=True,
            is_bundle=False,
            site=self.site,
            category=self.category,
            quantity=0
        )

        # Create component products for the bundle
        self.component1 = Product.objects.create(
            name="Component 1",
            quickbooks_id="comp1_qb_123",
            sku="comp-1",
            is_manufacture=False,
            site=self.site,
            category=self.category,
            quantity=100
        )

        self.component2 = Product.objects.create(
            name="Component 2",
            quickbooks_id="comp2_qb_123",
            sku="comp-2",
            is_manufacture=False,
            site=self.site,
            category=self.category,
            quantity=50
        )

        # Create product items (bundle composition)
        self.product_item1 = ProductItem.objects.create(
            product=self.bundle_product,
            item=self.component1,
            quantity=2  # 2 units of component1 per bundle
        )

        self.product_item2 = ProductItem.objects.create(
            product=self.bundle_product,
            item=self.component2,
            quantity=1  # 1 unit of component2 per bundle
        )

    @patch('quick_book.utils.orders.sync_orders')
    @patch('quick_book.utils.QuickBook')
    def test_manufacture_product_transaction_invoice_type(self, mock_quickbook_class, mock_sync_orders):
        """Test ProductStockTransaction save for manufacture product with INVOICE type."""
        self.create_manufacture_product_setup()

        # Mock QuickBook and client
        mock_quickbook = Mock()
        mock_client = Mock()
        mock_quickbook.get_client.return_value = mock_client
        mock_quickbook_class.return_value = mock_quickbook

        # Mock QuickBooks objects for components and inventory
        mock_comp1_qb_obj = Mock()
        mock_comp1_qb_obj.QtyOnHand = 100
        mock_comp2_qb_obj = Mock()
        mock_comp2_qb_obj.QtyOnHand = 50
        mock_inv_qb_obj = Mock()
        mock_inv_qb_obj.QtyOnHand = 0

        self.component1.get_quickbooks_object = Mock(return_value=mock_comp1_qb_obj)
        self.component2.get_quickbooks_object = Mock(return_value=mock_comp2_qb_obj)
        self.inventory_product.get_quickbooks_object = Mock(return_value=mock_inv_qb_obj)

        # Create transaction for manufacture product
        transaction = ProductStockTransaction.objects.create(
            product=self.inventory_product,
            quantity=5,  # Manufacturing 5 units
            site=self.site,
            order_line=self.order_line,
            type=ProductStockTransactionType.INVOICE,
            reason=TransactionReason.PURCHASE
        )

        # Verify QuickBook was initialized with correct site
        mock_quickbook_class.assert_called_once_with('gmall')

        # Verify component quantities were decreased in QuickBooks
        # Component 1: 2 units per bundle * 5 bundles = 10 units decreased
        self.assertEqual(mock_comp1_qb_obj.QtyOnHand, 90)  # 100 - 10
        mock_comp1_qb_obj.save.assert_called_once_with(qb=mock_client)

        # Component 2: 1 unit per bundle * 5 bundles = 5 units decreased
        self.assertEqual(mock_comp2_qb_obj.QtyOnHand, 45)  # 50 - 5
        mock_comp2_qb_obj.save.assert_called_once_with(qb=mock_client)

        # Verify inventory product quantity was increased in QuickBooks
        self.assertEqual(mock_inv_qb_obj.QtyOnHand, 5)  # 0 + 5
        mock_inv_qb_obj.save.assert_called_once_with(qb=mock_client)

        # Verify local product quantity was updated
        self.inventory_product.refresh_from_db()
        self.assertEqual(self.inventory_product.quantity, 5)

        # Verify transaction state was set correctly
        self.assertEqual(transaction.state, TransactionState.INCREASE)

        # Verify orders sync was called for INVOICE type
        mock_sync_orders.assert_called_once()

    @patch('quick_book.utils.orders.sync_orders')
    @patch('quick_book.utils.QuickBook')
    def test_manufacture_product_transaction_inventory_adjustment_type(self, mock_quickbook_class, mock_sync_orders):
        """Test ProductStockTransaction save for manufacture product with INVENTORY_ADJUSTMENT type."""
        self.create_manufacture_product_setup()

        # Mock QuickBook and client
        mock_quickbook = Mock()
        mock_client = Mock()
        mock_quickbook.get_client.return_value = mock_client
        mock_quickbook_class.return_value = mock_quickbook

        # Mock QuickBooks objects
        mock_comp1_qb_obj = Mock()
        mock_comp1_qb_obj.QtyOnHand = 100
        mock_comp2_qb_obj = Mock()
        mock_comp2_qb_obj.QtyOnHand = 50
        mock_inv_qb_obj = Mock()
        mock_inv_qb_obj.QtyOnHand = 0

        self.component1.get_quickbooks_object = Mock(return_value=mock_comp1_qb_obj)
        self.component2.get_quickbooks_object = Mock(return_value=mock_comp2_qb_obj)
        self.inventory_product.get_quickbooks_object = Mock(return_value=mock_inv_qb_obj)

        # Create transaction for manufacture product with INVENTORY_ADJUSTMENT type
        transaction = ProductStockTransaction.objects.create(
            product=self.inventory_product,
            quantity=3,
            site=self.site,
            type=ProductStockTransactionType.INVENTORY_ADJUSTMENT,
            reason=TransactionReason.ADMIN
        )

        # Verify QuickBook operations were performed
        mock_quickbook_class.assert_called_once_with('gmall')

        # Verify component quantities were decreased
        self.assertEqual(mock_comp1_qb_obj.QtyOnHand, 94)  # 100 - (2*3)
        self.assertEqual(mock_comp2_qb_obj.QtyOnHand, 47)  # 50 - (1*3)

        # Verify inventory product quantity was increased
        self.assertEqual(mock_inv_qb_obj.QtyOnHand, 3)

        # Verify orders sync was NOT called for INVENTORY_ADJUSTMENT type
        mock_sync_orders.assert_not_called()

    @patch('quick_book.utils.QuickBook')
    def test_manufacture_product_transaction_negative_quantity(self, mock_quickbook_class):
        """Test ProductStockTransaction save for manufacture product with negative quantity."""
        self.create_manufacture_product_setup()

        # Mock QuickBook and client
        mock_quickbook = Mock()
        mock_client = Mock()
        mock_quickbook.get_client.return_value = mock_client
        mock_quickbook_class.return_value = mock_quickbook

        # Mock QuickBooks objects
        mock_comp1_qb_obj = Mock()
        mock_comp1_qb_obj.QtyOnHand = 100
        mock_comp2_qb_obj = Mock()
        mock_comp2_qb_obj.QtyOnHand = 50
        mock_inv_qb_obj = Mock()
        mock_inv_qb_obj.QtyOnHand = 10

        self.component1.get_quickbooks_object = Mock(return_value=mock_comp1_qb_obj)
        self.component2.get_quickbooks_object = Mock(return_value=mock_comp2_qb_obj)
        self.inventory_product.get_quickbooks_object = Mock(return_value=mock_inv_qb_obj)

        # Create transaction with negative quantity (return/decrease)
        transaction = ProductStockTransaction.objects.create(
            product=self.inventory_product,
            quantity=-2,  # Returning 2 units
            site=self.site,
            type=ProductStockTransactionType.INVENTORY_ADJUSTMENT,
            reason=TransactionReason.RETURN
        )

        # Verify component quantities were increased (opposite of manufacturing)
        self.assertEqual(mock_comp1_qb_obj.QtyOnHand, 104)  # 100 + (2*2)
        self.assertEqual(mock_comp2_qb_obj.QtyOnHand, 52)  # 50 + (1*2)

        # Verify inventory product quantity was decreased
        self.assertEqual(mock_inv_qb_obj.QtyOnHand, 8)  # 10 - 2

        # Verify transaction state was set to DECREASE
        self.assertEqual(transaction.state, TransactionState.DECREASE)

    def test_non_manufacture_product_transaction_skips_manufacture_logic(self):
        """Test that non-manufacture products skip the manufacture logic."""
        # Create a regular (non-manufacture) product
        regular_product = Product.objects.create(
            name="Regular Product",
            quickbooks_id="reg_qb_123",
            sku="regular-product",
            is_manufacture=False,
            site=self.site,
            category=self.category,
            quantity=10
        )

        with patch('quick_book.utils.QuickBook') as mock_quickbook_class:
            # Create transaction for regular product
            transaction = ProductStockTransaction.objects.create(
                product=regular_product,
                quantity=5,
                site=self.site,
                type=ProductStockTransactionType.INVOICE,
                reason=TransactionReason.PURCHASE
            )

            # Verify QuickBook was not called for non-manufacture products
            mock_quickbook_class.assert_not_called()

            # Verify local product quantity was still updated
            regular_product.refresh_from_db()
            self.assertEqual(regular_product.quantity, 15)  # 10 + 5

            # Verify transaction state was set correctly
            self.assertEqual(transaction.state, TransactionState.INCREASE)

    @patch('quick_book.utils.QuickBook')
    def test_manufacture_product_site_mismatch_raises_error(self, mock_quickbook_class):
        """Test that site mismatch raises ValueError before manufacture logic."""
        self.create_manufacture_product_setup()

        # Create a different site
        different_site = Site.objects.create(domain="different.com", name="different")

        # Try to create transaction with mismatched site
        with self.assertRaises(ValueError) as context:
            ProductStockTransaction.objects.create(
                product=self.inventory_product,
                quantity=5,
                site=different_site,  # Different site
                type=ProductStockTransactionType.INVOICE,
                reason=TransactionReason.PURCHASE
            )

        self.assertEqual(str(context.exception), "product site should be the same with site")

        # Verify QuickBook was not called due to early error
        mock_quickbook_class.assert_not_called()

    @patch('quick_book.models.product_transaction.logger')
    @patch('quick_book.utils.QuickBook')
    def test_manufacture_product_transaction_logging(self, mock_quickbook_class, mock_logger):
        """Test that manufacture product transactions are properly logged."""
        self.create_manufacture_product_setup()

        # Mock QuickBook and client
        mock_quickbook = Mock()
        mock_client = Mock()
        mock_quickbook.get_client.return_value = mock_client
        mock_quickbook_class.return_value = mock_quickbook

        # Mock QuickBooks objects
        mock_comp1_qb_obj = Mock()
        mock_comp1_qb_obj.QtyOnHand = 100
        mock_comp2_qb_obj = Mock()
        mock_comp2_qb_obj.QtyOnHand = 50
        mock_inv_qb_obj = Mock()
        mock_inv_qb_obj.QtyOnHand = 0

        self.component1.get_quickbooks_object = Mock(return_value=mock_comp1_qb_obj)
        self.component2.get_quickbooks_object = Mock(return_value=mock_comp2_qb_obj)
        self.inventory_product.get_quickbooks_object = Mock(return_value=mock_inv_qb_obj)

        # Create transaction
        ProductStockTransaction.objects.create(
            product=self.inventory_product,
            quantity=2,
            site=self.site,
            type=ProductStockTransactionType.INVENTORY_ADJUSTMENT,
            reason=TransactionReason.ADMIN
        )

        # Verify logging calls were made
        expected_calls = [
            # Log for component 1
            f"Product {self.component1.name} of bundle {self.bundle_product.name} stock increased by {-4} in QuickBooks.",
            # Log for component 2  
            f"Product {self.component2.name} of bundle {self.bundle_product.name} stock increased by {-2} in QuickBooks.",
            # Log for inventory product
            f"Product {self.inventory_product.name} stock increased by {2} in QuickBooks."
        ]

        # Check that logger.info was called with expected messages
        self.assertEqual(mock_logger.info.call_count, 3)
        actual_calls = [call[0][0] for call in mock_logger.info.call_args_list]
        self.assertEqual(actual_calls, expected_calls)

    @patch('quick_book.utils.QuickBook')
    def test_manufacture_product_missing_bundle_product_error(self, mock_quickbook_class):
        """Test error handling when manufacture_bundle_product is missing."""
        # Create inventory product without corresponding bundle product
        inventory_product = Product.objects.create(
            name="Orphaned Inventory Product",
            quickbooks_id="orphan_qb_123",
            sku="p-manu-orphan",
            is_manufacture=True,
            site=self.site,
            category=self.category,
            quantity=0
        )

        # Mock QuickBook to avoid early initialization issues
        mock_quickbook = Mock()
        mock_client = Mock()
        mock_quickbook.get_client.return_value = mock_client
        mock_quickbook_class.return_value = mock_quickbook

        # Try to create transaction - should raise error when accessing bundle product
        with self.assertRaises(Product.DoesNotExist):
            ProductStockTransaction.objects.create(
                product=inventory_product,
                quantity=1,
                site=self.site,
                type=ProductStockTransactionType.INVENTORY_ADJUSTMENT,
                reason=TransactionReason.ADMIN
            )

    @patch('quick_book.utils.QuickBook')
    def test_manufacture_product_empty_bundle_items(self, mock_quickbook_class):
        """Test manufacture product transaction with bundle that has no items."""
        # Create bundle and inventory products without any ProductItems
        bundle_product = Product.objects.create(
            name="Empty Bundle Product",
            quickbooks_id="empty_bundle_qb_123",
            sku="manu-empty-bundle",
            is_manufacture=True,
            is_bundle=True,
            site=self.site,
            category=self.category,
            quantity=0
        )

        inventory_product = Product.objects.create(
            name="Empty Bundle Inventory",
            quickbooks_id="empty_inv_qb_123",
            sku="p-manu-empty-bundle",
            is_manufacture=True,
            site=self.site,
            category=self.category,
            quantity=0
        )

        # Mock QuickBook and client
        mock_quickbook = Mock()
        mock_client = Mock()
        mock_quickbook.get_client.return_value = mock_client
        mock_quickbook_class.return_value = mock_quickbook

        # Mock inventory product QuickBooks object
        mock_inv_qb_obj = Mock()
        mock_inv_qb_obj.QtyOnHand = 0
        inventory_product.get_quickbooks_object = Mock(return_value=mock_inv_qb_obj)

        # Create transaction - should work but only update inventory product
        transaction = ProductStockTransaction.objects.create(
            product=inventory_product,
            quantity=3,
            site=self.site,
            type=ProductStockTransactionType.INVENTORY_ADJUSTMENT,
            reason=TransactionReason.ADMIN
        )

        # Verify only inventory product was updated (no component processing)
        self.assertEqual(mock_inv_qb_obj.QtyOnHand, 3)
        mock_inv_qb_obj.save.assert_called_once_with(qb=mock_client)

        # Verify local quantity was updated
        inventory_product.refresh_from_db()
        self.assertEqual(inventory_product.quantity, 3)

    @patch('quick_book.utils.QuickBook')
    def test_manufacture_product_quickbooks_error_handling(self, mock_quickbook_class):
        """Test error handling when QuickBooks operations fail."""
        self.create_manufacture_product_setup()

        # Mock QuickBook to raise an exception
        mock_quickbook_class.side_effect = Exception("QuickBooks connection failed")

        # Transaction creation should still work but QuickBooks sync will fail
        with self.assertRaises(Exception) as context:
            ProductStockTransaction.objects.create(
                product=self.inventory_product,
                quantity=1,
                site=self.site,
                type=ProductStockTransactionType.INVENTORY_ADJUSTMENT,
                reason=TransactionReason.ADMIN
            )

        self.assertEqual(str(context.exception), "QuickBooks connection failed")

    def test_manufacture_product_transaction_str_representation(self):
        """Test string representation of ProductStockTransaction."""
        self.create_manufacture_product_setup()

        with patch('quick_book.utils.QuickBook'):
            transaction = ProductStockTransaction.objects.create(
                product=self.inventory_product,
                quantity=5,
                site=self.site,
                type=ProductStockTransactionType.INVOICE,
                reason=TransactionReason.PURCHASE
            )

            expected_str = f"{self.inventory_product.name}|5|{TransactionState.INCREASE}"
            self.assertEqual(str(transaction), expected_str)

    @patch('quick_book.utils.QuickBook')
    def test_manufacture_product_zero_quantity_transaction(self, mock_quickbook_class):
        """Test manufacture product transaction with zero quantity."""
        self.create_manufacture_product_setup()

        # Mock QuickBook and client
        mock_quickbook = Mock()
        mock_client = Mock()
        mock_quickbook.get_client.return_value = mock_client
        mock_quickbook_class.return_value = mock_quickbook

        # Mock QuickBooks objects
        mock_comp1_qb_obj = Mock()
        mock_comp1_qb_obj.QtyOnHand = 100
        mock_comp2_qb_obj = Mock()
        mock_comp2_qb_obj.QtyOnHand = 50
        mock_inv_qb_obj = Mock()
        mock_inv_qb_obj.QtyOnHand = 10

        self.component1.get_quickbooks_object = Mock(return_value=mock_comp1_qb_obj)
        self.component2.get_quickbooks_object = Mock(return_value=mock_comp2_qb_obj)
        self.inventory_product.get_quickbooks_object = Mock(return_value=mock_inv_qb_obj)

        # Create transaction with zero quantity
        transaction = ProductStockTransaction.objects.create(
            product=self.inventory_product,
            quantity=0,
            site=self.site,
            type=ProductStockTransactionType.INVENTORY_ADJUSTMENT,
            reason=TransactionReason.ADMIN
        )

        # Verify no changes to QuickBooks quantities
        self.assertEqual(mock_comp1_qb_obj.QtyOnHand, 100)  # No change
        self.assertEqual(mock_comp2_qb_obj.QtyOnHand, 50)  # No change
        self.assertEqual(mock_inv_qb_obj.QtyOnHand, 10)  # No change

        # Verify transaction state is still INCREASE (since 0 >= 0)
        self.assertEqual(transaction.state, TransactionState.INCREASE)
