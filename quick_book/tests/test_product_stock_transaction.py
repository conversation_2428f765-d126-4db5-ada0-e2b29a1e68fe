from unittest.mock import Mock, patch

from django.contrib.sites.models import Site
from django.test import TestCase

from constants import ProductStockTransactionType, TransactionState, TransactionReason
from quick_book.models import Product, Category, ProductStockTransaction


class ProductStockTransactionModelTest(TestCase):
    """Test cases for ProductStockTransaction model."""

    def setUp(self):
        """Set up test data."""
        # Create a test site
        self.site = Site.objects.create(domain="test.com", name="test")

        # Create a test category
        self.category = Category.objects.create(
            name="Test Category",
            quickbooks_id="test_qb_id",
            site=self.site
        )

        # Create a regular product
        self.product = Product.objects.create(
            name="Test Product",
            quickbooks_id="test_qb_id",
            sku="regular-123",
            quantity=100,
            site=self.site,
            category=self.category,
            is_bundle=False,
            is_manufacture=False
        )

        # Create a manufacture product
        self.manufacture_product = Product.objects.create(
            name="Manufacture Product",
            quickbooks_id="manu_qb_id",
            sku="manu-test-456",
            quantity=50,
            site=self.site,
            category=self.category,
            is_bundle=True,
            is_manufacture=True
        )

    def test_create_transaction_basic_fields(self):
        """Test creating a basic ProductStockTransaction."""
        transaction = ProductStockTransaction.objects.create(
            product=self.product,
            quantity=10,
            site=self.site,
            reason=TransactionReason.PURCHASE,
            type=ProductStockTransactionType.INVOICE
        )

        self.assertEqual(transaction.product, self.product)
        self.assertEqual(transaction.quantity, 10)
        self.assertEqual(transaction.site, self.site)
        self.assertEqual(transaction.reason, TransactionReason.PURCHASE)
        self.assertEqual(transaction.type, ProductStockTransactionType.INVOICE)
        self.assertEqual(transaction.state, TransactionState.INCREASE)

    def test_save_sets_state_based_on_quantity_positive(self):
        """Test that save method sets state to INCREASE for positive quantity."""
        transaction = ProductStockTransaction(
            product=self.product,
            quantity=15,
            site=self.site
        )

        with patch('quick_book.utils.QuickBook'), \
                patch('quick_book.utils.orders.sync_orders'):
            transaction.save()

        self.assertEqual(transaction.state, TransactionState.INCREASE)

    def test_save_sets_state_based_on_quantity_negative(self):
        """Test that save method sets state to DECREASE for negative quantity."""
        transaction = ProductStockTransaction(
            product=self.product,
            quantity=-5,
            site=self.site
        )

        with patch('quick_book.utils.QuickBook'), \
                patch('quick_book.utils.orders.sync_orders'):
            transaction.save()

        self.assertEqual(transaction.state, TransactionState.DECREASE)

    def test_save_sets_state_based_on_quantity_zero(self):
        """Test that save method sets state to INCREASE for zero quantity."""
        transaction = ProductStockTransaction(
            product=self.product,
            quantity=0,
            site=self.site
        )

        with patch('quick_book.utils.QuickBook'), \
                patch('quick_book.utils.orders.sync_orders'):
            transaction.save()

        self.assertEqual(transaction.state, TransactionState.INCREASE)

    def test_save_validates_product_site_match(self):
        """Test that save method validates product site matches transaction site."""
        # Create a different site
        other_site = Site.objects.create(domain="other.com", name="other")

        transaction = ProductStockTransaction(
            product=self.product,  # product belongs to self.site
            quantity=10,
            site=other_site  # different site
        )

        with self.assertRaises(ValueError) as context:
            transaction.save()

        self.assertEqual(str(context.exception), "product site should be the same with site")

    def test_save_updates_product_quantity(self):
        """Test that save method updates the product quantity."""
        initial_quantity = self.product.quantity
        transaction_quantity = 25

        transaction = ProductStockTransaction(
            product=self.product,
            quantity=transaction_quantity,
            site=self.site
        )

        with patch('quick_book.utils.QuickBook'), \
                patch('quick_book.utils.orders.sync_orders'):
            transaction.save()

        # Refresh product from database
        self.product.refresh_from_db()
        self.assertEqual(self.product.quantity, initial_quantity + transaction_quantity)

    @patch('quick_book.utils.QuickBook')
    def test_save_manufacture_product_quickbooks_integration(self, mock_quickbook_class):
        """Test QuickBooks integration for manufacture products."""
        # Mock QuickBooks client and objects
        mock_client = Mock()
        mock_quickbook_instance = Mock()
        mock_quickbook_instance.get_client.return_value = mock_client
        mock_quickbook_class.return_value = mock_quickbook_instance

        # Mock product QuickBooks objects
        mock_bundle_qb_object = Mock()
        mock_bundle_qb_object.QtyOnHand = 100
        mock_inventory_qb_object = Mock()
        mock_inventory_qb_object.QtyOnHand = 50

        # Mock the manufacture_bundle_product and its items
        mock_bundle_product = Mock()
        mock_product_item = Mock()
        mock_product_item.item = Mock()
        mock_product_item.item.get_quickbooks_object.return_value = mock_bundle_qb_object
        mock_product_item.quantity = 2
        mock_bundle_product.items.select_related.return_value.all.return_value = [mock_product_item]
        mock_bundle_product.name = "Test Bundle"

        self.manufacture_product.manufacture_bundle_product = mock_bundle_product
        self.manufacture_product.get_quickbooks_object = Mock(return_value=mock_inventory_qb_object)

        transaction = ProductStockTransaction(
            product=self.manufacture_product,
            quantity=5,
            site=self.site,
            type=ProductStockTransactionType.INVOICE
        )

        with patch('quick_book.utils.orders.sync_orders') as mock_sync_orders:
            transaction.save()

        # Verify QuickBook was instantiated with correct site
        mock_quickbook_class.assert_called_once_with('gmall')

        # Verify QuickBooks objects were updated
        mock_bundle_qb_object.save.assert_called_once_with(qb=mock_client)
        mock_inventory_qb_object.save.assert_called_once_with(qb=mock_client)

        # Verify quantities were updated correctly
        # Bundle product quantity should decrease by (item_quantity * transaction_quantity)
        self.assertEqual(mock_bundle_qb_object.QtyOnHand, 100 - (2 * 5))  # 90
        # Inventory product quantity should increase by transaction quantity
        self.assertEqual(mock_inventory_qb_object.QtyOnHand, 50 + 5)  # 55

    @patch('quick_book.utils.orders.sync_orders')
    @patch('quick_book.utils.QuickBook')
    def test_save_manufacture_product_with_order_line_syncs_orders(self, mock_quickbook_class, mock_sync_orders):
        """Test that orders are synced when transaction has order_line and is INVOICE type."""
        # Mock order and order line
        mock_order = Mock()
        mock_order.id = 123
        mock_order_line = Mock()
        mock_order_line.order_id = 123

        # Mock QuickBooks setup
        mock_client = Mock()
        mock_quickbook_instance = Mock()
        mock_quickbook_instance.get_client.return_value = mock_client
        mock_quickbook_class.return_value = mock_quickbook_instance

        # Mock manufacture product dependencies
        mock_bundle_product = Mock()
        mock_bundle_product.items.select_related.return_value.all.return_value = []
        mock_bundle_product.name = "Test Bundle"
        self.manufacture_product.manufacture_bundle_product = mock_bundle_product
        self.manufacture_product.get_quickbooks_object = Mock(return_value=Mock())

        transaction = ProductStockTransaction(
            product=self.manufacture_product,
            quantity=3,
            site=self.site,
            order_line=mock_order_line,
            type=ProductStockTransactionType.INVOICE
        )

        with patch('saleor.models.Order') as mock_order_model:
            mock_order_model.objects.filter.return_value = [mock_order]
            transaction.save()

        # Verify sync_orders was called with correct order
        mock_sync_orders.assert_called_once()
        call_args = mock_sync_orders.call_args[0][0]
        # The call should be made with a filtered queryset
        mock_order_model.objects.filter.assert_called_once_with(id=123)

    def test_save_non_manufacture_product_skips_quickbooks(self):
        """Test that non-manufacture products skip QuickBooks integration."""
        transaction = ProductStockTransaction(
            product=self.product,  # regular product, not manufacture
            quantity=10,
            site=self.site
        )

        with patch('quick_book.utils.QuickBook') as mock_quickbook_class, \
                patch('quick_book.utils.orders.sync_orders') as mock_sync_orders:
            transaction.save()

        # QuickBook should not be instantiated for non-manufacture products
        mock_quickbook_class.assert_not_called()
        mock_sync_orders.assert_not_called()

    def test_save_inventory_adjustment_type_skips_order_sync(self):
        """Test that INVENTORY_ADJUSTMENT type skips order synchronization."""
        # Mock manufacture product dependencies
        mock_bundle_product = Mock()
        mock_bundle_product.items.select_related.return_value.all.return_value = []
        mock_bundle_product.name = "Test Bundle"
        self.manufacture_product.manufacture_bundle_product = mock_bundle_product
        self.manufacture_product.get_quickbooks_object = Mock(return_value=Mock())

        mock_order_line = Mock()
        mock_order_line.order_id = 123

        transaction = ProductStockTransaction(
            product=self.manufacture_product,
            quantity=5,
            site=self.site,
            order_line=mock_order_line,
            type=ProductStockTransactionType.INVENTORY_ADJUSTMENT  # Not INVOICE
        )

        with patch('quick_book.utils.QuickBook') as mock_quickbook_class, \
                patch('quick_book.utils.orders.sync_orders') as mock_sync_orders:
            mock_quickbook_instance = Mock()
            mock_quickbook_instance.get_client.return_value = Mock()
            mock_quickbook_class.return_value = mock_quickbook_instance

            transaction.save()

        # QuickBooks should be called but orders should not be synced
        mock_quickbook_class.assert_called_once()
        mock_sync_orders.assert_not_called()


class ProductStockTransactionEdgeCasesTest(TestCase):
    """Test edge cases and error scenarios for ProductStockTransaction."""

    def setUp(self):
        """Set up test data."""
        self.site = Site.objects.create(domain="test.com", name="test")
        self.category = Category.objects.create(
            name="Test Category",
            quickbooks_id="test_qb_id",
            site=self.site
        )

        # Create a bundle product (is_bundle=True)
        self.bundle_product = Product.objects.create(
            name="Bundle Product",
            quickbooks_id="bundle_qb_id",
            sku="bundle-123",
            quantity=30,
            site=self.site,
            category=self.category,
            is_bundle=True,
            is_manufacture=False
        )

    def test_bundle_product_limit_choices_validation(self):
        """Test that bundle products are limited by the limit_choices_to constraint."""
        # This test verifies the model constraint limit_choices_to={"is_bundle": False}
        # In a real scenario, this would be enforced at the form/admin level
        transaction = ProductStockTransaction(
            product=self.bundle_product,  # is_bundle=True
            quantity=5,
            site=self.site
        )

        # The model itself doesn't enforce this constraint, but it's defined for forms/admin
        # We can still create the transaction, but it violates the intended constraint
        with patch('quick_book.utils.QuickBook'), \
                patch('quick_book.utils.orders.sync_orders'):
            transaction.save()

        self.assertEqual(transaction.product, self.bundle_product)

    @patch('quick_book.utils.QuickBook')
    def test_quickbooks_exception_handling(self, mock_quickbook_class):
        """Test handling of QuickBooks exceptions during save."""
        # Create manufacture product
        manufacture_product = Product.objects.create(
            name="Manufacture Product",
            quickbooks_id="manu_qb_id",
            sku="manu-test-789",
            quantity=20,
            site=self.site,
            category=self.category,
            is_manufacture=True
        )

        # Mock QuickBooks to raise an exception
        mock_quickbook_instance = Mock()
        mock_client = Mock()
        mock_quickbook_instance.get_client.return_value = mock_client
        mock_quickbook_class.return_value = mock_quickbook_instance

        # Mock manufacture_bundle_product to raise an exception
        mock_bundle_product = Mock()
        mock_bundle_product.items.select_related.return_value.all.side_effect = Exception("QuickBooks API Error")
        manufacture_product.manufacture_bundle_product = mock_bundle_product

        transaction = ProductStockTransaction(
            product=manufacture_product,
            quantity=5,
            site=self.site
        )

        # The exception should propagate and cause the transaction to fail
        with self.assertRaises(Exception) as context:
            transaction.save()

        self.assertEqual(str(context.exception), "QuickBooks API Error")

    def test_large_quantity_values(self):
        """Test handling of large quantity values."""
        product = Product.objects.create(
            name="Test Product",
            quickbooks_id="test_qb_id",
            sku="large-qty-test",
            quantity=1000000,
            site=self.site,
            category=self.category
        )

        large_quantity = 999999
        transaction = ProductStockTransaction(
            product=product,
            quantity=large_quantity,
            site=self.site
        )

        with patch('quick_book.utils.QuickBook'), \
                patch('quick_book.utils.orders.sync_orders'):
            transaction.save()

        product.refresh_from_db()
        self.assertEqual(product.quantity, 1000000 + large_quantity)
        self.assertEqual(transaction.state, TransactionState.INCREASE)

    def test_negative_large_quantity_values(self):
        """Test handling of large negative quantity values."""
        product = Product.objects.create(
            name="Test Product",
            quickbooks_id="test_qb_id",
            sku="neg-large-qty-test",
            quantity=1000000,
            site=self.site,
            category=self.category
        )

        large_negative_quantity = -500000
        transaction = ProductStockTransaction(
            product=product,
            quantity=large_negative_quantity,
            site=self.site
        )

        with patch('quick_book.utils.QuickBook'), \
                patch('quick_book.utils.orders.sync_orders'):
            transaction.save()

        product.refresh_from_db()
        self.assertEqual(product.quantity, 1000000 + large_negative_quantity)
        self.assertEqual(transaction.state, TransactionState.DECREASE)

    @patch('quick_book.utils.QuickBook')
    def test_manufacture_product_missing_bundle_product(self, mock_quickbook_class):
        """Test manufacture product without manufacture_bundle_product."""
        manufacture_product = Product.objects.create(
            name="Manufacture Product",
            quickbooks_id="manu_qb_id",
            sku="manu-no-bundle",
            quantity=10,
            site=self.site,
            category=self.category,
            is_manufacture=True
        )

        # Don't set manufacture_bundle_product (it will be None/missing)
        mock_quickbook_instance = Mock()
        mock_client = Mock()
        mock_quickbook_instance.get_client.return_value = mock_client
        mock_quickbook_class.return_value = mock_quickbook_instance

        transaction = ProductStockTransaction(
            product=manufacture_product,
            quantity=5,
            site=self.site
        )

        # This should raise an AttributeError when trying to access manufacture_bundle_product
        with self.assertRaises(AttributeError):
            transaction.save()

    def test_multiple_transactions_same_product(self):
        """Test multiple transactions on the same product."""
        product = Product.objects.create(
            name="Multi Transaction Product",
            quickbooks_id="multi_qb_id",
            sku="multi-123",
            quantity=100,
            site=self.site,
            category=self.category
        )

        # Create multiple transactions
        transactions_data = [
            (10, TransactionReason.PURCHASE),
            (-5, TransactionReason.RETURN),
            (15, TransactionReason.ADMIN),
            (-3, TransactionReason.RETURN)
        ]

        expected_final_quantity = 100  # Starting quantity

        for quantity, reason in transactions_data:
            transaction = ProductStockTransaction(
                product=product,
                quantity=quantity,
                reason=reason,
                site=self.site
            )

            with patch('quick_book.utils.QuickBook'), \
                    patch('quick_book.utils.orders.sync_orders'):
                transaction.save()

            expected_final_quantity += quantity

            # Verify state is set correctly
            if quantity >= 0:
                self.assertEqual(transaction.state, TransactionState.INCREASE)
            else:
                self.assertEqual(transaction.state, TransactionState.DECREASE)

        # Verify final product quantity
        product.refresh_from_db()
        self.assertEqual(product.quantity, expected_final_quantity)

        # Verify all transactions were created
        self.assertEqual(product.product_transactions.count(), 4)

    def test_concurrent_transaction_handling(self):
        """Test handling of concurrent transactions (basic test)."""
        product = Product.objects.create(
            name="Concurrent Test Product",
            quickbooks_id="concurrent_qb_id",
            sku="concurrent-123",
            quantity=50,
            site=self.site,
            category=self.category
        )

        # This is a basic test - in a real scenario you'd need more sophisticated
        # concurrency testing with threading or database-level testing
        transaction1 = ProductStockTransaction(
            product=product,
            quantity=10,
            site=self.site
        )

        transaction2 = ProductStockTransaction(
            product=product,
            quantity=-5,
            site=self.site
        )

        with patch('quick_book.utils.QuickBook'), \
                patch('quick_book.utils.orders.sync_orders'):
            transaction1.save()
            transaction2.save()

        product.refresh_from_db()
        self.assertEqual(product.quantity, 55)  # 50 + 10 - 5

    def test_transaction_with_all_optional_fields(self):
        """Test transaction with all optional fields set."""
        product = Product.objects.create(
            name="Full Fields Product",
            quickbooks_id="full_qb_id",
            sku="full-123",
            quantity=25,
            site=self.site,
            category=self.category
        )

        # Mock order line
        mock_order_line = Mock()
        mock_order_line.order_id = 456

        transaction = ProductStockTransaction(
            product=product,
            quantity=8,
            site=self.site,
            reason=TransactionReason.ADMIN,
            order_line=mock_order_line,
            type=ProductStockTransactionType.INVENTORY_ADJUSTMENT
        )

        with patch('quick_book.utils.QuickBook'), \
                patch('quick_book.utils.orders.sync_orders'):
            transaction.save()

        self.assertEqual(transaction.reason, TransactionReason.ADMIN)
        self.assertEqual(transaction.order_line, mock_order_line)
        self.assertEqual(transaction.type, ProductStockTransactionType.INVENTORY_ADJUSTMENT)
        self.assertEqual(transaction.state, TransactionState.INCREASE)

    def test_save_only_on_creation(self):
        """Test that special save logic only runs on creation, not updates."""
        product = Product.objects.create(
            name="Update Test Product",
            quickbooks_id="update_qb_id",
            sku="update-123",
            quantity=40,
            site=self.site,
            category=self.category
        )

        # Create transaction
        transaction = ProductStockTransaction(
            product=product,
            quantity=12,
            site=self.site
        )

        with patch('quick_book.utils.QuickBook') as mock_quickbook_class, \
                patch('quick_book.utils.orders.sync_orders') as mock_sync_orders:
            transaction.save()  # First save (creation)

        # Verify QuickBook was called during creation
        initial_call_count = mock_quickbook_class.call_count

        # Update the transaction
        transaction.reason = TransactionReason.ADMIN

        with patch('quick_book.utils.QuickBook') as mock_quickbook_class_update, \
                patch('quick_book.utils.orders.sync_orders') as mock_sync_orders_update:
            transaction.save()  # Second save (update)

        # Verify QuickBook was NOT called during update (because _state.adding is False)
        mock_quickbook_class_update.assert_not_called()
        mock_sync_orders_update.assert_not_called()


class ProductStockTransactionQuickBooksIntegrationTest(TestCase):
    """Test QuickBooks integration specific scenarios."""

    def setUp(self):
        """Set up test data."""
        self.site = Site.objects.create(domain="test.com", name="test")
        self.category = Category.objects.create(
            name="Test Category",
            quickbooks_id="test_qb_id",
            site=self.site
        )

    @patch('quick_book.utils.QuickBook')
    def test_quickbooks_client_initialization(self, mock_quickbook_class):
        """Test QuickBooks client is properly initialized."""
        manufacture_product = Product.objects.create(
            name="QB Init Test Product",
            quickbooks_id="qb_init_id",
            sku="manu-qb-init",
            quantity=15,
            site=self.site,
            category=self.category,
            is_manufacture=True
        )

        mock_quickbook_instance = Mock()
        mock_client = Mock()
        mock_quickbook_instance.get_client.return_value = mock_client
        mock_quickbook_class.return_value = mock_quickbook_instance

        # Mock dependencies
        mock_bundle_product = Mock()
        mock_bundle_product.items.select_related.return_value.all.return_value = []
        mock_bundle_product.name = "Test Bundle"
        manufacture_product.manufacture_bundle_product = mock_bundle_product
        manufacture_product.get_quickbooks_object = Mock(return_value=Mock())

        transaction = ProductStockTransaction(
            product=manufacture_product,
            quantity=7,
            site=self.site
        )

        with patch('quick_book.utils.orders.sync_orders'):
            transaction.save()

        # Verify QuickBook was initialized with 'gmall' site
        mock_quickbook_class.assert_called_once_with('gmall')
        mock_quickbook_instance.get_client.assert_called_once()

    @patch('quick_book.utils.QuickBook')
    def test_bundle_product_items_quantity_calculation(self, mock_quickbook_class):
        """Test correct quantity calculation for bundle product items."""
        manufacture_product = Product.objects.create(
            name="Bundle Calc Test Product",
            quickbooks_id="bundle_calc_id",
            sku="manu-bundle-calc",
            quantity=20,
            site=self.site,
            category=self.category,
            is_manufacture=True
        )

        # Setup QuickBooks mocks
        mock_client = Mock()
        mock_quickbook_instance = Mock()
        mock_quickbook_instance.get_client.return_value = mock_client
        mock_quickbook_class.return_value = mock_quickbook_instance

        # Create multiple bundle items with different quantities
        mock_item1_qb_object = Mock()
        mock_item1_qb_object.QtyOnHand = 100
        mock_item1 = Mock()
        mock_item1.item = Mock()
        mock_item1.item.get_quickbooks_object.return_value = mock_item1_qb_object
        mock_item1.quantity = 3  # Each manufacture unit uses 3 of this item

        mock_item2_qb_object = Mock()
        mock_item2_qb_object.QtyOnHand = 200
        mock_item2 = Mock()
        mock_item2.item = Mock()
        mock_item2.item.get_quickbooks_object.return_value = mock_item2_qb_object
        mock_item2.quantity = 5  # Each manufacture unit uses 5 of this item

        mock_bundle_product = Mock()
        mock_bundle_product.items.select_related.return_value.all.return_value = [mock_item1, mock_item2]
        mock_bundle_product.name = "Multi Item Bundle"
        manufacture_product.manufacture_bundle_product = mock_bundle_product

        # Mock inventory product
        mock_inventory_qb_object = Mock()
        mock_inventory_qb_object.QtyOnHand = 50
        manufacture_product.get_quickbooks_object = Mock(return_value=mock_inventory_qb_object)

        transaction_quantity = 4
        transaction = ProductStockTransaction(
            product=manufacture_product,
            quantity=transaction_quantity,
            site=self.site
        )

        with patch('quick_book.utils.orders.sync_orders'):
            transaction.save()

        # Verify item quantities were decreased correctly
        # Item 1: 100 - (3 * 4) = 88
        self.assertEqual(mock_item1_qb_object.QtyOnHand, 88)
        # Item 2: 200 - (5 * 4) = 180
        self.assertEqual(mock_item2_qb_object.QtyOnHand, 180)

        # Verify inventory product quantity was increased
        # Inventory: 50 + 4 = 54
        self.assertEqual(mock_inventory_qb_object.QtyOnHand, 54)

        # Verify save was called for all QuickBooks objects
        mock_item1_qb_object.save.assert_called_once_with(qb=mock_client)
        mock_item2_qb_object.save.assert_called_once_with(qb=mock_client)
        mock_inventory_qb_object.save.assert_called_once_with(qb=mock_client)

    @patch('quick_book.utils.orders.sync_orders')
    @patch('quick_book.utils.QuickBook')
    def test_order_sync_with_correct_filter(self, mock_quickbook_class, mock_sync_orders):
        """Test that order sync is called with correct order filter."""
        manufacture_product = Product.objects.create(
            name="Order Sync Test Product",
            quickbooks_id="order_sync_id",
            sku="manu-order-sync",
            quantity=25,
            site=self.site,
            category=self.category,
            is_manufacture=True
        )

        # Setup QuickBooks mocks
        mock_client = Mock()
        mock_quickbook_instance = Mock()
        mock_quickbook_instance.get_client.return_value = mock_client
        mock_quickbook_class.return_value = mock_quickbook_instance

        # Mock dependencies
        mock_bundle_product = Mock()
        mock_bundle_product.items.select_related.return_value.all.return_value = []
        mock_bundle_product.name = "Order Sync Bundle"
        manufacture_product.manufacture_bundle_product = mock_bundle_product
        manufacture_product.get_quickbooks_object = Mock(return_value=Mock())

        # Mock order line
        mock_order_line = Mock()
        mock_order_line.order_id = 789

        transaction = ProductStockTransaction(
            product=manufacture_product,
            quantity=6,
            site=self.site,
            order_line=mock_order_line,
            type=ProductStockTransactionType.INVOICE
        )

        with patch('saleor.models.Order') as mock_order_model:
            mock_queryset = Mock()
            mock_order_model.objects.filter.return_value = mock_queryset

            transaction.save()

            # Verify Order.objects.filter was called with correct order_id
            mock_order_model.objects.filter.assert_called_once_with(id=789)

            # Verify sync_orders was called with the filtered queryset
            mock_sync_orders.assert_called_once_with(mock_queryset)

    def test_transaction_inheritance_from_abstract_base_model(self):
        """Test that ProductStockTransaction inherits from AbstractBaseModel correctly."""
        product = Product.objects.create(
            name="Inheritance Test Product",
            quickbooks_id="inherit_id",
            sku="inherit-123",
            quantity=30,
            site=self.site,
            category=self.category
        )

        transaction = ProductStockTransaction.objects.create(
            product=product,
            quantity=8,
            site=self.site
        )

        # Test AbstractBaseModel fields
        self.assertTrue(hasattr(transaction, 'is_active'))
        self.assertTrue(hasattr(transaction, 'priority'))
        self.assertTrue(hasattr(transaction, 'created_at'))
        self.assertTrue(hasattr(transaction, 'updated_at'))

        # Test default values from AbstractBaseModel
        self.assertTrue(transaction.is_active)
        self.assertEqual(transaction.priority, 1000000)
        self.assertIsNotNone(transaction.created_at)
        self.assertIsNotNone(transaction.updated_at)

    def test_model_meta_and_database_constraints(self):
        """Test model metadata and database-level constraints."""
        product = Product.objects.create(
            name="Meta Test Product",
            quickbooks_id="meta_id",
            sku="meta-123",
            quantity=35,
            site=self.site,
            category=self.category
        )

        transaction = ProductStockTransaction.objects.create(
            product=product,
            quantity=9,
            site=self.site
        )

        # Test that state field has db_index=True (this is more of a model definition test)
        state_field = ProductStockTransaction._meta.get_field('state')
        self.assertTrue(state_field.db_index)

        # Test field max_lengths and choices
        self.assertEqual(state_field.max_length, 56)
        self.assertEqual(state_field.choices, TransactionState.choices)

        reason_field = ProductStockTransaction._meta.get_field('reason')
        self.assertEqual(reason_field.max_length, 30)
        self.assertEqual(reason_field.choices, TransactionReason.choices)

        type_field = ProductStockTransaction._meta.get_field('type')
        self.assertEqual(type_field.max_length, 50)
        self.assertEqual(type_field.choices, ProductStockTransactionType.choices)


class ProductStockTransactionErrorHandlingTest(TestCase):
    """Test error handling and edge cases."""

    def setUp(self):
        """Set up test data."""
        self.site = Site.objects.create(domain="test.com", name="test")
        self.category = Category.objects.create(
            name="Test Category",
            quickbooks_id="test_qb_id",
            site=self.site
        )

    def test_save_with_invalid_site_relationship(self):
        """Test save behavior with invalid site relationships."""
        site1 = Site.objects.create(domain="site1.com", name="site1")
        site2 = Site.objects.create(domain="site2.com", name="site2")

        product = Product.objects.create(
            name="Site Test Product",
            quickbooks_id="site_test_id",
            sku="site-123",
            quantity=40,
            site=site1,  # Product belongs to site1
            category=self.category
        )

        transaction = ProductStockTransaction(
            product=product,
            quantity=10,
            site=site2  # Transaction for site2 - should fail
        )

        with self.assertRaises(ValueError) as context:
            transaction.save()

        self.assertEqual(str(context.exception), "product site should be the same with site")

    @patch('quick_book.utils.QuickBook')
    def test_quickbooks_object_save_failure(self, mock_quickbook_class):
        """Test handling when QuickBooks object save fails."""
        manufacture_product = Product.objects.create(
            name="QB Save Fail Product",
            quickbooks_id="qb_fail_id",
            sku="manu-qb-fail",
            quantity=15,
            site=self.site,
            category=self.category,
            is_manufacture=True
        )

        # Setup QuickBooks mocks
        mock_client = Mock()
        mock_quickbook_instance = Mock()
        mock_quickbook_instance.get_client.return_value = mock_client
        mock_quickbook_class.return_value = mock_quickbook_instance

        # Mock QuickBooks object that fails to save
        mock_qb_object = Mock()
        mock_qb_object.save.side_effect = Exception("QuickBooks save failed")

        mock_item = Mock()
        mock_item.item = Mock()
        mock_item.item.get_quickbooks_object.return_value = mock_qb_object
        mock_item.quantity = 2

        mock_bundle_product = Mock()
        mock_bundle_product.items.select_related.return_value.all.return_value = [mock_item]
        mock_bundle_product.name = "Failing Bundle"
        manufacture_product.manufacture_bundle_product = mock_bundle_product

        transaction = ProductStockTransaction(
            product=manufacture_product,
            quantity=3,
            site=self.site
        )

        with self.assertRaises(Exception) as context:
            transaction.save()

        self.assertEqual(str(context.exception), "QuickBooks save failed")

    def test_transaction_with_zero_quantity_edge_cases(self):
        """Test various edge cases with zero quantity."""
        product = Product.objects.create(
            name="Zero Qty Test Product",
            quickbooks_id="zero_qty_id",
            sku="zero-123",
            quantity=50,
            site=self.site,
            category=self.category
        )

        # Test zero quantity transaction
        transaction = ProductStockTransaction(
            product=product,
            quantity=0,
            site=self.site
        )

        with patch('quick_book.utils.QuickBook'), \
                patch('quick_book.utils.orders.sync_orders'):
            transaction.save()

        # Zero quantity should still be treated as INCREASE
        self.assertEqual(transaction.state, TransactionState.INCREASE)

        # Product quantity should remain unchanged
        product.refresh_from_db()
        self.assertEqual(product.quantity, 50)

    def test_logging_verification(self):
        """Test that appropriate logging occurs during save operations."""
        manufacture_product = Product.objects.create(
            name="Logging Test Product",
            quickbooks_id="logging_id",
            sku="manu-logging",
            quantity=20,
            site=self.site,
            category=self.category,
            is_manufacture=True
        )

        with patch('quick_book.utils.QuickBook') as mock_quickbook_class, \
                patch('quick_book.models.product_transaction.logger') as mock_logger:
            # Setup QuickBooks mocks
            mock_client = Mock()
            mock_quickbook_instance = Mock()
            mock_quickbook_instance.get_client.return_value = mock_client
            mock_quickbook_class.return_value = mock_quickbook_instance

            # Mock bundle product with items
            mock_item = Mock()
            mock_item.item = Mock()
            mock_item.item.get_quickbooks_object.return_value = Mock()
            mock_item.quantity = 1
            mock_item.item.name = "Test Item"

            mock_bundle_product = Mock()
            mock_bundle_product.items.select_related.return_value.all.return_value = [mock_item]
            mock_bundle_product.name = "Logging Bundle"
            manufacture_product.manufacture_bundle_product = mock_bundle_product
            manufacture_product.get_quickbooks_object = Mock(return_value=Mock())
            manufacture_product.name = "Logging Product"

            transaction = ProductStockTransaction(
                product=manufacture_product,
                quantity=2,
                site=self.site
            )

            with patch('quick_book.utils.orders.sync_orders'):
                transaction.save()

            # Verify logging was called (logger.info should be called twice)
            self.assertEqual(mock_logger.info.call_count, 2)

            # Check that log messages contain expected information
            log_calls = mock_logger.info.call_args_list
            self.assertIn("Test Item", str(log_calls[0]))
            self.assertIn("Logging Bundle", str(log_calls[0]))
            self.assertIn("Logging Product", str(log_calls[1]))
