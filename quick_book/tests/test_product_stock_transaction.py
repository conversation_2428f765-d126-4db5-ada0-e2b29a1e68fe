from unittest.mock import Mock, patch, MagicMock
from django.test import TestCase
from django.contrib.sites.models import Site
from django.db import transaction
from django.core.exceptions import ValidationError

from quick_book.models import Product, Category, ProductStockTransaction
from constants import ProductStockTransactionType, TransactionState, TransactionReason


class ProductStockTransactionModelTest(TestCase):
    """Test cases for ProductStockTransaction model."""

    def setUp(self):
        """Set up test data."""
        # Create a test site
        self.site = Site.objects.create(domain="test.com", name="test")
        
        # Create a test category
        self.category = Category.objects.create(
            name="Test Category",
            quickbooks_id="test_qb_id",
            site=self.site
        )
        
        # Create a regular product
        self.product = Product.objects.create(
            name="Test Product",
            quickbooks_id="test_qb_id",
            sku="regular-123",
            quantity=100,
            site=self.site,
            category=self.category,
            is_bundle=False,
            is_manufacture=False
        )
        
        # Create a manufacture product
        self.manufacture_product = Product.objects.create(
            name="Manufacture Product",
            quickbooks_id="manu_qb_id",
            sku="manu-test-456",
            quantity=50,
            site=self.site,
            category=self.category,
            is_bundle=True,
            is_manufacture=True
        )

    def test_create_transaction_basic_fields(self):
        """Test creating a basic ProductStockTransaction."""
        transaction = ProductStockTransaction.objects.create(
            product=self.product,
            quantity=10,
            site=self.site,
            reason=TransactionReason.PURCHASE,
            type=ProductStockTransactionType.INVOICE
        )
        
        self.assertEqual(transaction.product, self.product)
        self.assertEqual(transaction.quantity, 10)
        self.assertEqual(transaction.site, self.site)
        self.assertEqual(transaction.reason, TransactionReason.PURCHASE)
        self.assertEqual(transaction.type, ProductStockTransactionType.INVOICE)
        self.assertEqual(transaction.state, TransactionState.INCREASE)

    def test_save_sets_state_based_on_quantity_positive(self):
        """Test that save method sets state to INCREASE for positive quantity."""
        transaction = ProductStockTransaction(
            product=self.product,
            quantity=15,
            site=self.site
        )
        
        with patch('quick_book.utils.QuickBook'), \
             patch('quick_book.utils.orders.sync_orders'):
            transaction.save()
        
        self.assertEqual(transaction.state, TransactionState.INCREASE)

    def test_save_sets_state_based_on_quantity_negative(self):
        """Test that save method sets state to DECREASE for negative quantity."""
        transaction = ProductStockTransaction(
            product=self.product,
            quantity=-5,
            site=self.site
        )
        
        with patch('quick_book.utils.QuickBook'), \
             patch('quick_book.utils.orders.sync_orders'):
            transaction.save()
        
        self.assertEqual(transaction.state, TransactionState.DECREASE)

    def test_save_sets_state_based_on_quantity_zero(self):
        """Test that save method sets state to INCREASE for zero quantity."""
        transaction = ProductStockTransaction(
            product=self.product,
            quantity=0,
            site=self.site
        )
        
        with patch('quick_book.utils.QuickBook'), \
             patch('quick_book.utils.orders.sync_orders'):
            transaction.save()
        
        self.assertEqual(transaction.state, TransactionState.INCREASE)

    def test_save_validates_product_site_match(self):
        """Test that save method validates product site matches transaction site."""
        # Create a different site
        other_site = Site.objects.create(domain="other.com", name="other")
        
        transaction = ProductStockTransaction(
            product=self.product,  # product belongs to self.site
            quantity=10,
            site=other_site  # different site
        )
        
        with self.assertRaises(ValueError) as context:
            transaction.save()
        
        self.assertEqual(str(context.exception), "product site should be the same with site")

    def test_save_updates_product_quantity(self):
        """Test that save method updates the product quantity."""
        initial_quantity = self.product.quantity
        transaction_quantity = 25
        
        transaction = ProductStockTransaction(
            product=self.product,
            quantity=transaction_quantity,
            site=self.site
        )
        
        with patch('quick_book.utils.QuickBook'), \
             patch('quick_book.utils.orders.sync_orders'):
            transaction.save()
        
        # Refresh product from database
        self.product.refresh_from_db()
        self.assertEqual(self.product.quantity, initial_quantity + transaction_quantity)

    @patch('quick_book.utils.QuickBook')
    def test_save_manufacture_product_quickbooks_integration(self, mock_quickbook_class):
        """Test QuickBooks integration for manufacture products."""
        # Mock QuickBooks client and objects
        mock_client = Mock()
        mock_quickbook_instance = Mock()
        mock_quickbook_instance.get_client.return_value = mock_client
        mock_quickbook_class.return_value = mock_quickbook_instance
        
        # Mock product QuickBooks objects
        mock_bundle_qb_object = Mock()
        mock_bundle_qb_object.QtyOnHand = 100
        mock_inventory_qb_object = Mock()
        mock_inventory_qb_object.QtyOnHand = 50
        
        # Mock the manufacture_bundle_product and its items
        mock_bundle_product = Mock()
        mock_product_item = Mock()
        mock_product_item.item = Mock()
        mock_product_item.item.get_quickbooks_object.return_value = mock_bundle_qb_object
        mock_product_item.quantity = 2
        mock_bundle_product.items.select_related.return_value.all.return_value = [mock_product_item]
        mock_bundle_product.name = "Test Bundle"
        
        self.manufacture_product.manufacture_bundle_product = mock_bundle_product
        self.manufacture_product.get_quickbooks_object = Mock(return_value=mock_inventory_qb_object)
        
        transaction = ProductStockTransaction(
            product=self.manufacture_product,
            quantity=5,
            site=self.site,
            type=ProductStockTransactionType.INVOICE
        )
        
        with patch('quick_book.utils.orders.sync_orders') as mock_sync_orders:
            transaction.save()
        
        # Verify QuickBook was instantiated with correct site
        mock_quickbook_class.assert_called_once_with('gmall')
        
        # Verify QuickBooks objects were updated
        mock_bundle_qb_object.save.assert_called_once_with(qb=mock_client)
        mock_inventory_qb_object.save.assert_called_once_with(qb=mock_client)
        
        # Verify quantities were updated correctly
        # Bundle product quantity should decrease by (item_quantity * transaction_quantity)
        self.assertEqual(mock_bundle_qb_object.QtyOnHand, 100 - (2 * 5))  # 90
        # Inventory product quantity should increase by transaction quantity
        self.assertEqual(mock_inventory_qb_object.QtyOnHand, 50 + 5)  # 55

    @patch('quick_book.utils.orders.sync_orders')
    @patch('quick_book.utils.QuickBook')
    def test_save_manufacture_product_with_order_line_syncs_orders(self, mock_quickbook_class, mock_sync_orders):
        """Test that orders are synced when transaction has order_line and is INVOICE type."""
        # Mock order and order line
        mock_order = Mock()
        mock_order.id = 123
        mock_order_line = Mock()
        mock_order_line.order_id = 123
        
        # Mock QuickBooks setup
        mock_client = Mock()
        mock_quickbook_instance = Mock()
        mock_quickbook_instance.get_client.return_value = mock_client
        mock_quickbook_class.return_value = mock_quickbook_instance
        
        # Mock manufacture product dependencies
        mock_bundle_product = Mock()
        mock_bundle_product.items.select_related.return_value.all.return_value = []
        mock_bundle_product.name = "Test Bundle"
        self.manufacture_product.manufacture_bundle_product = mock_bundle_product
        self.manufacture_product.get_quickbooks_object = Mock(return_value=Mock())
        
        transaction = ProductStockTransaction(
            product=self.manufacture_product,
            quantity=3,
            site=self.site,
            order_line=mock_order_line,
            type=ProductStockTransactionType.INVOICE
        )
        
        with patch('saleor.models.Order') as mock_order_model:
            mock_order_model.objects.filter.return_value = [mock_order]
            transaction.save()
        
        # Verify sync_orders was called with correct order
        mock_sync_orders.assert_called_once()
        call_args = mock_sync_orders.call_args[0][0]
        # The call should be made with a filtered queryset
        mock_order_model.objects.filter.assert_called_once_with(id=123)

    def test_save_non_manufacture_product_skips_quickbooks(self):
        """Test that non-manufacture products skip QuickBooks integration."""
        transaction = ProductStockTransaction(
            product=self.product,  # regular product, not manufacture
            quantity=10,
            site=self.site
        )
        
        with patch('quick_book.utils.QuickBook') as mock_quickbook_class, \
             patch('quick_book.utils.orders.sync_orders') as mock_sync_orders:
            transaction.save()
        
        # QuickBook should not be instantiated for non-manufacture products
        mock_quickbook_class.assert_not_called()
        mock_sync_orders.assert_not_called()

    def test_save_inventory_adjustment_type_skips_order_sync(self):
        """Test that INVENTORY_ADJUSTMENT type skips order synchronization."""
        # Mock manufacture product dependencies
        mock_bundle_product = Mock()
        mock_bundle_product.items.select_related.return_value.all.return_value = []
        mock_bundle_product.name = "Test Bundle"
        self.manufacture_product.manufacture_bundle_product = mock_bundle_product
        self.manufacture_product.get_quickbooks_object = Mock(return_value=Mock())
        
        mock_order_line = Mock()
        mock_order_line.order_id = 123
        
        transaction = ProductStockTransaction(
            product=self.manufacture_product,
            quantity=5,
            site=self.site,
            order_line=mock_order_line,
            type=ProductStockTransactionType.INVENTORY_ADJUSTMENT  # Not INVOICE
        )
        
        with patch('quick_book.utils.QuickBook') as mock_quickbook_class, \
             patch('quick_book.utils.orders.sync_orders') as mock_sync_orders:
            mock_quickbook_instance = Mock()
            mock_quickbook_instance.get_client.return_value = Mock()
            mock_quickbook_class.return_value = mock_quickbook_instance
            
            transaction.save()
        
        # QuickBooks should be called but orders should not be synced
        mock_quickbook_class.assert_called_once()
        mock_sync_orders.assert_not_called()

    def test_str_representation(self):
        """Test the string representation of ProductStockTransaction."""
        transaction = ProductStockTransaction(
            product=self.product,
            quantity=15,
            state=TransactionState.INCREASE
        )
        
        expected_str = f"{self.product.name}|15|{TransactionState.INCREASE}"
        self.assertEqual(str(transaction), expected_str)

    def test_transaction_atomic_decorator(self):
        """Test that save method is wrapped in atomic transaction."""
        # This test verifies the @transaction.atomic decorator is working
        transaction = ProductStockTransaction(
            product=self.product,
            quantity=10,
            site=self.site
        )
        
        # Mock an exception during save to test rollback
        with patch('quick_book.utils.QuickBook') as mock_quickbook_class:
            mock_quickbook_class.side_effect = Exception("QuickBooks error")
            
            with self.assertRaises(Exception):
                transaction.save()
        
        # Verify the transaction was not saved due to rollback
        self.assertFalse(ProductStockTransaction.objects.filter(id=transaction.id).exists())
        
        # Verify product quantity was not changed due to rollback
        self.product.refresh_from_db()
        self.assertEqual(self.product.quantity, 100)  # Original quantity

    def test_foreign_key_relationships(self):
        """Test foreign key relationships are properly set."""
        transaction = ProductStockTransaction.objects.create(
            product=self.product,
            quantity=5,
            site=self.site
        )
        
        # Test product relationship
        self.assertEqual(transaction.product, self.product)
        self.assertIn(transaction, self.product.product_transactions.all())
        
        # Test site relationship
        self.assertEqual(transaction.site, self.site)
        self.assertIn(transaction, self.site.product_stock_transactions.all())

    def test_default_values(self):
        """Test default values are set correctly."""
        transaction = ProductStockTransaction(
            product=self.product,
            site=self.site
        )
        
        self.assertEqual(transaction.quantity, 0)
        self.assertEqual(transaction.state, TransactionState.INCREASE)
        self.assertEqual(transaction.reason, TransactionReason.PURCHASE)
        self.assertEqual(transaction.type, ProductStockTransactionType.INVOICE)
        self.assertIsNone(transaction.order_line)
