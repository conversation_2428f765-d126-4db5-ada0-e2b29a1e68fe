from accounts.serializers import UserSerializer
from core import serializers

from service_center.models import ServiceCenter


class AdminServiceCenterSerializer(serializers.ModelSerializer):
    user = UserSerializer(fields=("id", "full_name", "username"))
    address = serializers.SerializerMethodField()
    agent = serializers.SerializerMethodField()
    fast_agent = serializers.SerializerMethodField()
    factors = serializers.SerializerMethodField()

    class Meta:
        model = ServiceCenter
        fields = (
            "id",
            "name",
            "user",
            "address",
            "grade",
            "supper_plus",
            "appearance",
            "agent",
            "fast_agent",
            "factors",
            "visit_status",
        )

    def get_agent(self, obj):
        if obj.agent:
            return UserSerializer(obj.agent.user, fields=("id", "full_name", "username")).data
        else:
            return "ندارد"

    def get_fast_agent(self, obj):
        if obj.fast_agent:
            return UserSerializer(obj.fast_agent.user, fields=("id", "full_name", "username")).data
        else:
            return "ندارد"

    def get_address(self, obj):
        return {"region": obj.address.region.name, "region_id": obj.address.region.id}

    def get_factors(self, obj):
        return {"count": 0, "total_amount": 0, "compare": ""}
