from accounts.serializers import UserSerializer
from core import serializers
from service_center.models import ServiceCenter


class SyncServiceCenterSerializer(serializers.ModelSerializer):
    user = UserSerializer(fields=(
        "id",
        "username",
        "first_name",
        "last_name",
        "full_name",
        "mobile_number",
        "email",
        "national_id",
        "birthplace_id"
    ))
    address = serializers.SerializerMethodField()

    class Meta:
        model = ServiceCenter
        fields = (
            "id",
            "user",
            "name",
            "phone_number",
            "address",
        )

    def get_address(self, obj):
        addresses = {
            "location_address": {
                "id": obj.default_location_address.id,
                "city": obj.default_location_address.region.city.name,
                "region": obj.default_location_address.region.name,
                "region_id": obj.default_location_address.region.id,
                "postal_code": obj.default_location_address.postal_code,
                "description": obj.default_location_address.description,
                "latitude": obj.default_location_address.latitude,
                "longitude": obj.default_location_address.longitude,
                "state": obj.default_location_address.region.city.state.name
            }
        }
        if obj.default_billing_address:
            addresses["billing_address"] = {
                "id": obj.default_billing_address.id,
                "city": obj.default_billing_address.region.city.name,
                "region": obj.default_billing_address.region.name,
                "region_id": obj.default_billing_address.region.id,
                "postal_code": obj.default_billing_address.postal_code,
                "description": obj.default_billing_address.description,
                "latitude": obj.default_billing_address.latitude,
                "longitude": obj.default_billing_address.longitude,
            }
        if obj.default_shipping_address:
            addresses["shipping_address"] = {
                "id": obj.default_shipping_address.id,
                "city": obj.default_shipping_address.region.city.name,
                "region": obj.default_shipping_address.region.name,
                "region_id": obj.default_shipping_address.region.id,
                "postal_code": obj.default_shipping_address.postal_code,
                "description": obj.default_shipping_address.description,
                "latitude": obj.default_shipping_address.latitude,
                "longitude": obj.default_shipping_address.longitude,
            }
        return addresses
