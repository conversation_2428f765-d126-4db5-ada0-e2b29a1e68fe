import logging

from core import serializers
from service_center.models import Bill

logger = logging.getLogger("service_center")


class LoadBillsSerializer(serializers.ModelSerializer):
    customer = serializers.SerializerMethodField(read_only=True)
    verifier = serializers.SerializerMethodField(read_only=True)
    car = serializers.CharField(source="user_car.car_detail.car.name", read_only=True)
    created_at = serializers.SerializerMethodField()
    created_time = serializers.SerializerMethodField()
    items = serializers.SerializerMethodField()
    total_charge_price = serializers.SerializerMethodField()

    class Meta:
        model = Bill
        fields = (
            "id",
            "is_verified",
            "created_at",
            "created_time",
            "customer",
            "verifier",
            "car",
            "current_kilometer",
            "next_kilometer",
            "items",
            "total_charge_price",

        )

    @staticmethod
    def get_customer(obj):
        return {
            "fullname": obj.customer.full_name,
            "mobile": obj.customer.username,
        }

    @staticmethod
    def get_verifier(obj):
        return {
            "fullname": obj.verifier.full_name,
            "mobile": obj.verifier.username,
        } if obj.verifier else {}

    def get_created_at(self, obj):
        return obj.created_at.date()

    def get_created_time(self, obj):
        return obj.created_at.time()

    def get_items(self, obj):
        return [{
            "product": item.content_object.product.name if not item.content_object.free_gift else
            "{} + {}".format(item.content_object.product.name, item.content_object.free_gift),
            "quantity": item.content_object.quantity,
            "charge_price": item.charge_price
        } for item in obj.items.all() if item.is_order_line]

    def get_total_charge_price(self, obj):
        return self.context["charge_prices"][obj.id]
