from core import serializers

from service_center.models import ServiceCenter


class ServiceCenterMapSerializer(serializers.ModelSerializer):
    lat = serializers.SerializerMethodField()
    lng = serializers.SerializerMethodField()

    class Meta:
        model = ServiceCenter
        fields = ("id", "name", "lat", "lng")

    def get_lat(self, obj):
        return obj.address.latitude

    def get_lng(self, obj):
        return obj.address.longitude
