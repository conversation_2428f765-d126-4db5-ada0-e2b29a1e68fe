from core.serializers import ModelSerializer, Serializer<PERSON>ethodField

from service_center.models import WorkShift


class WorkShiftSerializer(ModelSerializer):
    employees_count = SerializerMethodField()
    pits_count = SerializerMethodField()

    class Meta:
        model = WorkShift
        fields = (
            "first_shift_start",
            "first_shift_end",
            "second_shift_start",
            "second_shift_end",
            "week_days_first_shift",
            "week_days_second_shift",
            "first_weekend_first_shift",
            "first_weekend_second_shift",
            "employees_count",
            "second_weekend_first_shift",
            "second_weekend_second_shift",
            "holidays_first_shift",
            "pits_count",
            "holidays_second_shift",
            "cars_per_hour",
        )

    def get_employees_count(self, obj):
        return obj.service_center.employees_count

    def get_pits_count(self, obj):
        return obj.service_center.pits_count
