from core import serializers
from service_center.models import ServiceCategory
from quick_book.models import Category
from saleor.models import OrderLine
from service_center.models import ServiceCenterServiceType
from saleor.utils import get_email
from service_center.utils.calculate_charge_price import get_order_line_base_on_category
from translation.utils.translate import get_object_translation


class OrderLineServiceTypeSerializer(serializers.ModelSerializer):
    order_line_id = serializers.IntegerField(source="id")
    order_name = serializers.SerializerMethodField()
    delivery_type = serializers.CharField(source="get_service_type_display")

    class Meta:
        model = OrderLine
        fields = (
            "order_line_id",
            "order_name",
            "delivery_type",
            "quantity",
            "expired_at",
        )

    @staticmethod
    def get_order_name(obj):
        if obj.free_gift :
            return "{} + {}".format(obj.product.name, obj.free_gift)

        return obj.product.name

class ServiceCategorySerializer(serializers.ModelSerializer):
    def __init__(self, *args, **kwargs):
        self.mobile_number = kwargs.pop("mobile_number")
        self.lang = kwargs.pop("lang")
        super().__init__(*args, **kwargs)

    category = serializers.SerializerMethodField()

    class Meta:
        model = ServiceCenterServiceType
        fields = (
            "category",
        )

    def get_category(self, instance):
        category_resp = {
            "id": instance.service_type.category.id,
            "image": str(instance.service_type.category.image) if instance.service_type.category.image else None,
            "name": instance.service_type.category.name,
        }
        category_resp.update(get_object_translation(instance.service_type.category, self.lang))
        return category_resp

    def to_representation(self, instance):
        data = super().to_representation(instance)
        order_lines = get_order_line_base_on_category(
            instance.service_center.order_lines.select_related("product").available_order_lines(
                get_email(self.mobile_number)
            ),data["category"]["id"] )
        data["orders"] = OrderLineServiceTypeSerializer(
            order_lines, many=True
        ).data
        data["service_count"] = order_lines.count()
        return data
