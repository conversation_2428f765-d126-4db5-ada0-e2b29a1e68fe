from accounts.serializers import UserSerializer
from agent.serializers import AgentSerializer
from core import serializers
from files.models import Image

from service_center.models import ServiceCenter, ServiceType, ServiceCenterServiceTypeCapacity, ServiceCenterServiceType
from translation.utils import translation_checker


class ServiceCenterSerializer(serializers.ModelSerializer):
    def __init__(self, *args, **kwargs):
        self.agent = kwargs.pop("agent", False)
        self.commercial_register_code = kwargs.pop("commercial_register_code", None)
        self.lang = kwargs.pop("lang", "en")
        super().__init__(*args, **kwargs)

    edit_location_address_permission = serializers.SerializerMethodField()
    user = UserSerializer(fields=(
        "id",
        "username",
        "first_name",
        "last_name",
        "full_name",
        "mobile_number",
        "email",
        "national_id",
        "birthplace_id",
        "national_id"
    ))
    agent = AgentSerializer(fields=("id", "full_name"))
    fast_agent = AgentSerializer(fields=("id", "full_name"))
    address = serializers.SerializerMethodField()
    agent_service_center = serializers.SerializerMethodField()
    image = serializers.SerializerMethodField()
    types = serializers.SerializerMethodField()
    commercial_register_code = serializers.SerializerMethodField()
    weekday_shifts = serializers.SerializerMethodField()
    shift_capacity = serializers.SerializerMethodField()
    visit_counts = serializers.SerializerMethodField()
    phone_number = serializers.SerializerMethodField()
    needs_stand = serializers.SerializerMethodField()

    class Meta:
        model = ServiceCenter
        fields = (
            "id",
            "user",
            "name",
            "phone_number",
            "agent_service_center",
            "appearance",
            "image",
            "shelves_count",
            "pits_count",
            "employees_count",
            "center_size",
            "inventory_storage_size",
            "has_lobby",
            "ownership_type",
            "rent_end_date",
            "agent",
            "fast_agent",
            "visit_status",
            "address",
            "is_active",
            "service_bay_count",
            "customer_lounge_size",
            "storefront_area_size",
            # "parking_area",
            # "building_area",
            "experience_in_year",
            "commercial_register_code",
            "jac_equipment_count",
            "weekday_shifts",
            "shift_capacity",
            "administrative_area_size",
            "types",
            "license_expiration",
            "sponsor_civil_number",
            "sponsor_name",
            "vat_number",
            "visit_counts",
            "visit_counts",
            "daily_user_visit",
            "needs_stand",
            "foreman_civil_number",
            "foreman_first_name",
            "foreman_last_name",
            "foreman_phone_number",
            "top_seller_brand",
            "engine_oil_cost",
            "engine_oil_service_cost",
            "oil_filter_cost",
            "oil_filter_service_cost",
            "edit_location_address_permission"
        )

    def get_edit_location_address_permission(self, obj):
        return self.agent.user.has_perm("agent.can_update_location_address") if self.agent else False

    def get_needs_stand(self, obj):
        return 0 if not obj.needs_stand else 1

    def get_phone_number(self, obj):
        return obj.phone_number or obj.user.username

    def get_visit_counts(self, obj):
        return obj.visit_counts()

    def get_shift_capacity(self, obj):
        from service_center.serializers import ServiceCenterServiceTypeCapacitySerializer
        sc_service_types = list(obj.service_types.filter(is_active=True).values_list("id", flat=True))
        sc_st_capacities = ServiceCenterServiceTypeCapacity.objects.filter(
            service_center_service_type__id__in=sc_service_types
        ).distinct()
        return ServiceCenterServiceTypeCapacitySerializer(sc_st_capacities, many=True).data

    def get_weekday_shifts(self, obj):
        from . import ServiceCenterConfigSerializer
        from service_center.models import ServiceCenterConfig
        if ServiceCenterConfig.objects.filter(service_center=obj).exists():
            return ServiceCenterConfigSerializer(obj.config).data
        return ""

    def get_address(self, obj):
        addresses = {
            "location_address": {
                "id": obj.default_location_address.id,
                "city": obj.default_location_address.region.city.name,
                "city_display_name": translation_checker(
                    obj.default_location_address.region.city.display_name.capitalize(), self.lang),
                "state": obj.default_location_address.region.city.state.name,
                "state_display_name": translation_checker(
                    obj.default_location_address.region.city.state.display_name.capitalize(), self.lang),
                "region": obj.default_location_address.region.name,
                "region_display_name": translation_checker(
                    obj.default_location_address.region.display_name.capitalize(), self.lang),
                "region_id": obj.default_location_address.region.id,
                "postal_code": obj.default_location_address.postal_code,
                "description": obj.default_location_address.description,
                "latitude": obj.default_location_address.latitude,
                "longitude": obj.default_location_address.longitude,
            }
        }
        if obj.default_billing_address:
            addresses["billing_address"] = {
                "id": obj.default_billing_address.id,
                "city": obj.default_billing_address.region.city.name,
                "city_display_name": translation_checker(
                    obj.default_billing_address.region.city.display_name.capitalize(), self.lang),
                "state": obj.default_billing_address.region.city.state.name,
                "state_display_name": translation_checker(
                    obj.default_billing_address.region.city.state.display_name.capitalize(), self.lang),
                "region": obj.default_billing_address.region.name,
                "region_display_name": translation_checker(obj.default_billing_address.region.display_name.capitalize(),
                                                           self.lang),
                "region_id": obj.default_billing_address.region.id,
                "postal_code": obj.default_billing_address.postal_code,
                "description": obj.default_billing_address.description,
                "latitude": obj.default_billing_address.latitude,
                "longitude": obj.default_billing_address.longitude,
            }
        if obj.default_shipping_address:
            addresses["shipping_address"] = {
                "id": obj.default_shipping_address.id,
                "city": obj.default_shipping_address.region.city.name,
                "city_display_name": translation_checker(
                    obj.default_shipping_address.region.city.display_name.capitalize(), self.lang),
                "state": obj.default_shipping_address.region.city.state.name,
                "state_display_name": translation_checker(
                    obj.default_shipping_address.region.city.state.display_name.capitalize(), self.lang),
                "region": obj.default_shipping_address.region.name,
                "region_display_name": translation_checker(
                    obj.default_shipping_address.region.display_name.capitalize(), self.lang),
                "region_id": obj.default_shipping_address.region.id,
                "postal_code": obj.default_shipping_address.postal_code,
                "description": obj.default_shipping_address.description,
                "latitude": obj.default_shipping_address.latitude,
                "longitude": obj.default_shipping_address.longitude,
            }

        return addresses

    def get_types(self, obj):
        from .service_center_type import ServiceCenterServiceTypeSerializer
        type_ids = list(obj.service_types.all().values_list("service_type__id", flat=True))
        sc_service_types = ServiceCenterServiceType.objects.filter(
            service_type__id__in=type_ids,
            service_center=obj,
            is_active=True
        )
        # service_types = ServiceType.objects.filter(id__in=type_ids)
        return ServiceCenterServiceTypeSerializer(
            sc_service_types, context={"lang": self.lang}, many=True
        ).data

    def get_image(self, obj):
        from files.serializers import ImageSerializer
        try:
            images = Image.objects.filter(content_type__model="servicecenter", object_id=obj.id).all().order_by("id")
            values_list = images.values_list("name", flat=True).distinct()
            image_ids = []
            names = []

            for value in values_list:
                if value in names:
                    continue
                names.append(value)
                image_id = images.filter(name=value).first().pk
                if image_id not in image_ids:
                    image_ids.append(images.filter(name=value).last().pk)

            return ImageSerializer(images.filter(id__in=image_ids), many=True).data
        except Exception:
            return ""

    def get_agent_service_center(self, obj):
        if self.agent and self.agent.fast_visit:
            return obj.fast_agent == self.agent
        else:
            return obj.agent == self.agent

    def get_commercial_register_code(self, obj):
        return obj.commercial_register_code


class OmanServiceCenterResponseSerializer(ServiceCenterSerializer):


    class Meta(ServiceCenterSerializer.Meta):
        fields = ServiceCenterSerializer.Meta.fields + ("is_car_rental",)


class TurkeyAgentResponseSerializer(ServiceCenterSerializer):
    foreman_national_id = serializers.SerializerMethodField()

    class Meta(ServiceCenterSerializer.Meta):
        fields = ServiceCenterSerializer.Meta.fields + ("foreman_national_id",)

    def get_foreman_national_id(self, obj):
        return obj.foreman_civil_number
