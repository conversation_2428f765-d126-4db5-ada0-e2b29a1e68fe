from core import serializers

from products.models import ProductServiceCenterStockTransaction


class ServiceCenterTransactionStockDashboardSerializer(serializers.ModelSerializer):
    service_center_name = serializers.CharField()
    product_name = serializers.CharField()

    class Meta:
        model = ProductServiceCenterStockTransaction
        fields = (
            "id",
            "created_at",
            "quantity",
            "service_center_balance_percentage",
            "state",
            "stock_type",
            "creator_type",
            "service_center_id",
            "service_center_name",
            "product_id",
            "product_name"
        )

