from core import serializers

from service_center.models import ServiceCenterConfig
from ..service_center_shift import ServiceCenterShiftSerializer


class ServiceCenterConfigDashboardSerializer(serializers.ModelSerializer):
    sat = serializers.SerializerMethodField()
    sun = serializers.SerializerMethodField()
    mon = serializers.SerializerMethodField()
    tue = serializers.SerializerMethodField()
    wed = serializers.SerializerMethodField()
    thu = serializers.SerializerMethodField()
    fri = serializers.SerializerMethodField()
    happy_hour = serializers.SerializerMethodField()
    rush_hour = serializers.SerializerMethodField()

    class Meta:
        model = ServiceCenterConfig
        fields = (
            "id",
            "sat",
            "sun",
            "mon",
            "tue",
            "wed",
            "thu",
            "fri",
            "happy_hour",
            "rush_hour"
        )

    def get_sat(self, obj):
        return ServiceCenterShiftSerializer(obj.sat.all(), many=True).data

    def get_sun(self, obj):
        return ServiceCenterShiftSerializer(obj.sun.all(), many=True).data

    def get_mon(self, obj):
        return ServiceCenterShiftSerializer(obj.mon.all(), many=True).data

    def get_tue(self, obj):
        return ServiceCenterShiftSerializer(obj.tue.all(), many=True).data

    def get_wed(self, obj):
        return ServiceCenterShiftSerializer(obj.wed.all(), many=True).data

    def get_thu(self, obj):
        return ServiceCenterShiftSerializer(obj.thu.all(), many=True).data

    def get_fri(self, obj):
        return ServiceCenterShiftSerializer(obj.fri.all(), many=True).data

    def get_happy_hour(self, obj):
        return ServiceCenterShiftSerializer(obj.happy_hour.all(), many=True).data

    def get_rush_hour(self, obj):
        return ServiceCenterShiftSerializer(obj.rush_hour.all(), many=True).data
