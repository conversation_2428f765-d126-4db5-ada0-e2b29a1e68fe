from core import serializers

from products.models import ProductServiceCenterStock


class ServiceCenterStockDashboardSerializer(serializers.ModelSerializer):
    service_center_name = serializers.CharField()
    product_name = serializers.CharField()

    class Meta:
        model = ProductServiceCenterStock
        fields = (
            "id",
            "created_at",
            "stock",
            "stock_type",
            "service_center_id",
            "service_center_name",
            "product_id",
            "product_name"
        )

