from core import serializers

from service_center.models import ServiceCenterServiceTypeCapacity


class ServiceCenterServiceTypeCapacityDashboardSerializer(serializers.ModelSerializer):
    service_type = serializers.SerializerMethodField()

    class Meta:
        model = ServiceCenterServiceTypeCapacity
        fields = ("service_type", "capacity", "shift")

    def get_service_type(self, obj):
        return obj.service_center_service_type.service_type_id
