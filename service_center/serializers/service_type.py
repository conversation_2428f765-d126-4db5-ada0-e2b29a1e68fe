from core.serializers import ModelSerializer
from rest_framework import serializers

from service_center.models import ServiceType
from translation.utils import get_object_translation


class ServiceTypeSerializer(ModelSerializer):

    class Meta:
        model = ServiceType
        fields = ("id", "name")


    def to_representation(self, instance):
        data = super().to_representation(instance)
        data.update(get_object_translation(instance, self.context.get("lang", "en")))
        return data