from accounts.models import User
from cars.models import UserCar
from cars.serializers import UserCarSerializer
from rest_framework import serializers


class LoadCustomerInfoSerializer(serializers.ModelSerializer):
    def __init__(self, *args, **kwargs):
        self.has_order = kwargs.pop("has_order", False)
        super().__init__(*args, **kwargs)

    full_name = serializers.SerializerMethodField()
    username = serializers.SerializerMethodField()
    cars = serializers.SerializerMethodField()
    is_coupon = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = (
            "username",
            "full_name",
            "cars",
            "is_coupon",
        )

    def get_username(self, obj):
        return obj.username

    def get_full_name(self, obj):
        return obj.get_full_name() if obj.first_name and obj.last_name else ""

    def get_cars(self, obj):
        return UserCarSerializer(
            UserCar.objects.select_related(
                "user",
                "car_detail",
                "car_detail__car__brand"
            ).filter(user_id=obj.id, is_active=True), many=True
        ).data

    def get_is_coupon(self, obj):
        return self.has_order
