import jdatetime
from core.serializers import ModelSerializer
from django.contrib.sites.models import Site
from rest_framework import serializers

from service_center.models import ServiceCenterEmployee


class AdminServiceCenterEmployeeSerializer(ModelSerializer):
    first_name = serializers.SerializerMethodField()
    last_name = serializers.SerializerMethodField()
    mobile_number = serializers.SerializerMethodField()
    credit = serializers.SerializerMethodField()
    date = serializers.SerializerMethodField()

    class Meta:
        model = ServiceCenterEmployee
        fields = (
            "pk",
            "mobile_number",
            "last_name",
            "first_name",
            "credit",
            "is_active",
            "date",
        )

    def get_credit(self, obj):
        return obj.user.get_credit(Site.objects.get(pk=1))

    def get_last_name(self, obj):
        return obj.user.last_name

    def get_mobile_number(self, obj):
        return obj.user.username

    def get_first_name(self, obj):
        return obj.user.first_name

    def get_date(self, obj):
        return jdatetime.date.fromgregorian(
            day=obj.created_at.day, month=obj.created_at.month, year=obj.created_at.year
        ).strftime("%Y/%m/%d")
