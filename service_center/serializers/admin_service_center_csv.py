from core import serializers

from service_center.models import ServiceCenter


class AdminServiceCenterSCVSerializer(serializers.ModelSerializer):
    username = serializers.SerializerMethodField()
    fullname = serializers.SerializerMethodField()
    address_address = serializers.SerializerMethodField()
    address_city = serializers.SerializerMethodField()
    address_region = serializers.SerializerMethodField()
    address_state = serializers.SerializerMethodField()
    agent_username = serializers.SerializerMethodField()
    agent_fullname = serializers.SerializerMethodField()
    fast_agent_username = serializers.SerializerMethodField()
    fast_agent_fullname = serializers.SerializerMethodField()
    supper_plus = serializers.SerializerMethodField()
    types = serializers.SerializerMethodField()

    class Meta:
        model = ServiceCenter
        fields = (
            "id",
            "username",
            "fullname",
            "name",
            "address",
            "agent_username",
            "agent_fullname",
            "fast_agent_username",
            "fast_agent_fullname",
            "grade",
            "supper_plus",
            "visit_status",
            "address_address",
            "address_city",
            "address_region",
            "address_state",
            "types",
        )

    def get_types(self, obj):
        return ", ".join(list(obj.types.values_list("name", flat=True)))

    def get_username(self, obj):
        return obj.user.username

    def get_supper_plus(self, obj):
        return "دارد" if obj.supper_plus else "ندارد"

    def get_fullname(self, obj):
        return obj.user.full_name

    def get_address_address(self, obj):
        return obj.address.address

    def get_address_city(self, obj):
        return obj.address.region.city.name

    def get_address_state(self, obj):
        return obj.address.region.city.state.name

    def get_address_region(self, obj):
        return obj.address.region.name

    def get_agent_fullname(self, obj):
        if obj.agent:
            return obj.agent.user.full_name
        else:
            return "ندارد"

    def get_agent_username(self, obj):
        if obj.agent:
            return obj.agent.user.username
        else:
            return "ندارد"

    def get_fast_agent_fullname(self, obj):
        if obj.fast_agent:
            return obj.fast_agent.user.full_name
        else:
            return "ندارد"

    def get_fast_agent_username(self, obj):
        if obj.fast_agent:
            return obj.fast_agent.user.username
        else:
            return "ندارد"
