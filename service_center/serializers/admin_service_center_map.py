from core import serializers

from service_center.models import ServiceCenter


class AdminServiceCenterMapSerializer(serializers.ModelSerializer):
    i = serializers.SerializerMethodField()
    n = serializers.SerializerMethodField()
    l = serializers.SerializerMethodField()
    g = serializers.SerializerMethodField()

    class Meta:
        model = ServiceCenter
        fields = (
            "i",
            "n",
            "l",
            "g",
        )

    def get_i(self, obj):
        return obj.id

    def get_n(self, obj):
        return obj.name

    def get_l(self, obj):
        return obj.address.latitude

    def get_g(self, obj):
        return obj.address.longitude
