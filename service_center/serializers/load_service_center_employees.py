from core.serializers import ModelSerializer
from rest_framework import serializers

from service_center.models import ServiceCenterEmployee


class LoadServiceCenterEmployeeSerializer(ModelSerializer):
    full_name = serializers.SerializerMethodField()
    user_id = serializers.SerializerMethodField()
    bills_count = serializers.SerializerMethodField()
    mobile_number = serializers.SerializerMethodField()

    class Meta:
        model = ServiceCenterEmployee
        fields = (
            "user_id",
            "mobile_number",
            "service_center",
            "full_name",
            "is_active",
            "bills_count",
        )

    def get_full_name(self, obj):
        return obj.user.get_full_name()

    def get_mobile_number(self, obj):
        return obj.user.username

    def get_user_id(self, obj):
        return obj.user.id
