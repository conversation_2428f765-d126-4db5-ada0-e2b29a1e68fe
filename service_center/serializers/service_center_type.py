from core import serializers

from service_center.models import ServiceCenterServiceType


class ServiceCenterServiceTypeSerializer(serializers.ModelSerializer):
    service_type = serializers.SerializerMethodField()

    class Meta:
        model = ServiceCenterServiceType
        fields = (
            "service_center",
            "service_type"
        )

    def get_service_type(self, obj):
        from . import ServiceTypeSerializer
        return ServiceTypeSerializer(obj.service_type, context=self.context).data
