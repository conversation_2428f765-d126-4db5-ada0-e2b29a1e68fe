from core import serializers
from saleor.models import OrderLine
from service_center.models import BillItem

from django.db.models import F, Sum, OuterRef, Subquery, Prefetch
from django.db.models.functions import Coalesce

from django.contrib.contenttypes.models import ContentType


class ServiceCenterStockSerializer(serializers.Serializer):
    product__name = serializers.CharField()
    product_id = serializers.IntegerField()
    delivered = serializers.IntegerField()
    hig_share = serializers.IntegerField()
    sc_share = serializers.IntegerField()

    def to_representation(self, instance):
        data = super().to_representation(instance)

        product_id = instance["product_id"]
        service_center_id = self.context.get('service_center_id')
        data["services"] = 0
        bill_items = (
            BillItem.objects.filter(
                bill__service_center_id=service_center_id,
                content_type=ContentType.objects.get_for_model(OrderLine),
            )
            .prefetch_related(
                Prefetch(
                    'content_object__product__items',  # Prefetch the bundle items
                    queryset=OrderLine.objects.filter(product_id=product_id)  # Adjust YourItemModel
                )
            )
        )

        for bill in bill_items:
            if bill.content_object.product.is_bundle:
                try:
                    item = bill.content_object.product.items.get(item_id=product_id)
                    data["services"] = item.quantity * bill.content_object.quantity

                except Exception:
                    pass

            else:
                if bill.content_object.product.id == product_id:
                    data["services"] = bill.content_object.quantity

        remain_stock = data["hig_share"] - data["services"]
        data["remain_stock"] = remain_stock
        return data
