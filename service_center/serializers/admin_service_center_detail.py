from accounts.serializers import UserSerializer
from core import serializers

from service_center.models import ServiceCenter


class AdminServiceCenterDetailSerializer(serializers.ModelSerializer):
    user = UserSerializer(fields=("id", "first_name", "last_name", "username"))
    agent = serializers.SerializerMethodField()
    fast_agent = serializers.SerializerMethodField()
    service_types = serializers.SerializerMethodField()
    types = serializers.SerializerMethodField()
    sites = serializers.SerializerMethodField()

    class Meta:
        model = ServiceCenter
        fields = (
            "id",
            "user",
            "name",
            "agent",
            "grade",
            "types",
            "appearance",
            "phone_number",
            "appearance",
            "car_type",
            "shelves_count",
            "pits_count",
            "employees_count",
            "assistants_count",
            "garage_aria",
            "store_aria",
            "has_lobby",
            "ownership_type",
            "tableau_type",
            "service_types",
            "fast_agent",
            "visit_status",
            "address",
            "supper_plus",
            "sites",
        )

    def get_agent(self, obj):
        return {"id": obj.agent.id if obj.agent else None, "name": obj.agent.user.full_name if obj.agent else None}

    def get_fast_agent(self, obj):
        return {"id": obj.fast_agent.id, "full_name": obj.fast_agent.user.full_name}

    def get_types(self, obj):
        return list(obj.types.values_list("id", flat=True))

    def get_sites(self, obj):
        return list(obj.sites.values_list("id", flat=True))

    def get_service_types(self, obj):
        return list(obj.service_types.values_list("id", flat=True))
