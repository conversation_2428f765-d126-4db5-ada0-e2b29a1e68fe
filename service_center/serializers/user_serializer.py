from rest_framework import serializers
from accounts.models import User
from django.utils.translation import gettext


class UserSerializer(serializers.ModelSerializer):
    mobile_number = serializers.CharField(allow_blank=False)

    class Meta:
        model = User
        fields = (
            "mobile_number",
            "first_name",
            "last_name",
            "national_id"
        )

    def create(self, validated_data):
        user, created = User.objects.get_or_create(
            username=validated_data['mobile_number'],
            defaults={
                "first_name": validated_data['first_name'],
                "last_name": validated_data['last_name'],
                "national_id": validated_data['national_id'],
            }
        )

        if created is False:
            return False, gettext("employee_already_exists")

        return True, user.id
