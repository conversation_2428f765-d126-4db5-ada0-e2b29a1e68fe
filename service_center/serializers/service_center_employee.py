from core.serializers import ModelSerializer
from rest_framework import serializers

from service_center.models import ServiceCenterEmployee, Bill


class ServiceCenterEmployeeSerializer(ModelSerializer):
    first_name = serializers.SerializerMethodField()
    last_name = serializers.SerializerMethodField()
    mobile_number = serializers.SerializerMethodField()
    number_of_bills = serializers.IntegerField()

    class Meta:
        model = ServiceCenterEmployee
        fields = ("id", "mobile_number", "last_name", "first_name", "is_active", "number_of_bills", "service_center")

    def get_last_name(self, obj):
        return obj.user.last_name

    def get_mobile_number(self, obj):
        return obj.user.username

    def get_first_name(self, obj):
        return obj.user.first_name
