import jdatetime
from core.serializers import ModelSerializer
from rest_framework import serializers

from service_center.models import Vacation


class VacationSerializer(ModelSerializer):
    date_name = serializers.SerializerMethodField()
    raw_date = serializers.SerializerMethodField()

    class Meta:
        model = Vacation
        fields = ("id", "date_name", "raw_date")

    def get_date_name(self, obj):
        jdatetime.set_locale("fa_IR")
        return jdatetime.date.fromgregorian(day=obj.date.day, month=obj.date.month, year=obj.date.year).strftime(
            "%a %d %b"
        )

    def get_raw_date(self, obj):
        return jdatetime.date.fromgregorian(day=obj.date.day, month=obj.date.month, year=obj.date.year).strftime(
            "%Y/%m/%d"
        )
