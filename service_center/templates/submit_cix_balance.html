{% extends 'base_bootstrap.html' %}
{% load static %}

{% block head_style_tags %}
    <link href='http://fonts.googleapis.com/css?family=Roboto' rel='stylesheet' type='text/css'>
    <style>
        * {
            font-family: 'Roboto', sans-serif !important;
        }

        body {
            padding: 40px;
        }
    </style>
    {{ block.super }}
{% endblock %}
{% block head_title_tag %}<title>Submit Cix Balance</title>{% endblock %}
{% block body %}
    <script>
        if (window.history.replaceState) {
            window.history.replaceState(null, null, window.location.href);
        }
    </script>
    <div>
        <h4>Submit Cix Balance</h4>
    </div>
    <div style="color: lightslategray">
        <form method="POST" enctype="multipart/form-data">
            {% csrf_token %}
            <div class="row form-group">
                <div class="col-md-5">
                    <div>
                        <label style="color: midnightblue">Excel</label>
                        <input type="file" class="form-control" name="excel" required>
                    </div>
                </div>
            </div>
            <div class="row form-group mt-3">
                <div class="mt-3">
                    <button type="submit" class="btn btn-success w-100">Import</button>
                </div>
            </div>
        </form>
    </div>
    <div>
        <p class="text-warning">
        columns order:<br>
        col 1: Order line Id<br>
        col 2: product smt Id<br>
        col 3: product price<br>
        col 4: sale amount<br>
        col 5: national code<br>
        col 6: mobile number<br>
        col 7: chassis number<br>
        col 8: engine number<br>
        <span style="color:red">notice: if order line Id column is full, other columns can be empty, otherwise data of all other columns should be given!!</span>
    </p>
    </div>
    {% if response %}
    {% endif %}
    {% if exceptions %}
        <div class="mt-5">
            <h4 class="text-danger">Exceptions</h4>
            <ul>
                {% for d in exceptions %}
                    <li>{{ d }}</li>
                {% endfor %}
            </ul>
        </div>
    {% endif %}
    {% if submitted %}
        <div class="mt-5">
            <h4 class="text-danger">Submitted</h4>
            <ul>
                {% for s in submitted %}
                    <li>{{ s }}</li>
                {% endfor %}
            </ul>
        </div>
    {% endif %}
{% endblock %}