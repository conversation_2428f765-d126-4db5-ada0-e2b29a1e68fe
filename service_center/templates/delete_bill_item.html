{% extends 'base_bootstrap.html' %}
{% load static %}

{% block head_title_tag %}<title>Delete BillItem</title>{% endblock %}
{% block body %}
    <script>
        if ( window.history.replaceState ) {
            window.history.replaceState( null, null, window.location.href );
        }
    </script>
    <div class="p-5 font-roboto">
        <h4>Delete BillItem</h4>
        <div style="color: lightslategray">
            <form method="POST" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="row form-group mt-2">
                    <div class="col-md-6">
                        <label style="color: midnightblue">id</label>
                        <input type="text" class="form-control" name="bill_item_id" value="{{ billItem.id }}" placeholder="BillItem id, ex: 1434" required>
                    </div>
                </div>
                <div class="row form-group mt-4">
                    <div class="col-md-6">
                        <button type="submit" class="btn btn-primary">Submit</button>
                    </div>
                </div>
            </form>
        </div>
        {% if result %}
        <div class="mt-5">
            <h4>result</h4>
            <p>{{ result }}</p>
        </div>
        {% endif %}
        {% if exceptions %}
        <div class="mt-5">
            <h4 class="text-danger">Exceptions</h4>
            <ul>
            {% for d in exceptions %}
                <li>{{ d }}</li>
            {% endfor %}
            </ul>
        </div>
        {% endif %}
    </div>
{% endblock %}
