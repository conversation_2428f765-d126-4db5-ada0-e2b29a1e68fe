<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Import Service Center Data</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f0f2f5;
        }
        /* Custom styles for form rendering if needed */
        .form-field p {
            margin-bottom: 1rem;
        }
        .form-field label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #374151;
        }
        .form-field .helptext {
            font-size: 0.875rem;
            color: #6b7280;
            margin-top: 0.5rem;
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center p-4">
    <div class="bg-white p-8 rounded-xl shadow-xl w-full max-w-2xl border border-gray-200">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800">Import Service Center Data</h1>
            <p class="text-gray-500 mt-2">Upload your Excel file to process and view the data.</p>
        </div>

        <form method="post" enctype="multipart/form-data" class="space-y-6">
            {% csrf_token %}
            <div class="form-field">
                {{ form.as_p }}
            </div>
            <button type="submit" class="w-full flex items-center justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-lg font-semibold text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path></svg>
                Upload and Process
            </button>
        </form>

        {% if upload_success %}
            <div class="mt-10 pt-6 border-t border-gray-200">
                <h3 class="text-2xl font-semibold text-center text-gray-800 mb-6">Import Results</h3>
                <div class="flex items-center justify-center p-4 mb-4 text-sm text-green-700 bg-green-100 rounded-lg" role="alert">
                  <svg class="w-5 h-5 inline mr-3" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>
                    <span class="font-medium">Success! </span>
                </div>

            </div>
        {% endif %}
    </div>
</body>
</html>