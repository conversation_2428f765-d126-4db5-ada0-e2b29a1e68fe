<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Export Service Center data</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f0f2f5;
        }
        /* Style for form errors */
        .form-errors {
            color: #dc2626; /* red-600 */
            font-size: 0.875rem; /* text-sm */
            margin-top: 0.5rem;
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center p-4">
    <div class="bg-white p-8 rounded-lg shadow-xl w-full max-w-md border border-gray-200">
        <h1 class="text-3xl font-bold text-center text-gray-800 mb-6">Export Service Center data</h1>

        <form method="post" action="{% url 'service_center:admin-service-center-export' %}" class="space-y-6">
            {% csrf_token %}

            <div>
                <label for="{{ form.service_center_ids.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ form.service_center_ids.label }}
                </label>
                {{ form.service_center_ids }}
                {% if form.service_center_ids.help_text %}
                <p class="mt-2 text-sm text-gray-500">{{ form.service_center_ids.help_text }}</p>
                {% endif %}
                {% if form.service_center_ids.errors %}
                <div class="form-errors">
                    {{ form.service_center_ids.errors }}
                </div>
                {% endif %}
            </div>

            <button
                type="submit"
                class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-lg font-semibold text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out"
            >
                Generate Export Link
            </button>
        </form>

        {% if download_link %}
        <div id="download-result" class="mt-8 p-6 bg-green-50 border border-green-200 rounded-lg shadow-sm text-center">
            <h2 class="text-xl font-semibold text-green-800 mb-4">Your Export Is Ready!</h2>
            <p class="text-gray-700 mb-4">Click the link below to Export your data file:</p>
            <a
                href="{{ download_link }}" target="_blank"
                class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition duration-150 ease-in-out"
            >
                <svg class="-ml-1 mr-3 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v3.586L7.707 9.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 10.586V7z" clip-rule="evenodd" />
                </svg>
                Export data
            </a>
            <p class="text-xs text-gray-500 mt-4">Note: This is a simulated link for demonstration purposes.</p>
        </div>
        {% endif %}

    </div>
</body>
</html>