{% extends 'base_bootstrap.html' %}
{% load static %}
{% block head_style_tags %}
    <link href='http://fonts.googleapis.com/css?family=Roboto' rel='stylesheet' type='text/css'>
    <style>
        * {
            font-family: 'Roboto', sans-serif !important;
        }
        body {
            padding: 40px;
        }
    </style>
    {{ block.super }}
{% endblock %}
{% block head_title_tag %}
    <title>Manual Submit Bil</title>
{% endblock %}
{% block body %}
    <script>
        if ( window.history.replaceState ) {
            window.history.replaceState( null, null, window.location.href );
        }
    </script>
    <div class="container">
        <div>
            <h4>Manual Submit Bill</h4>
        </div>
        <div style="color: lightslategray">
            <form method="POST" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="row form-group">
                    <input type="hidden" name="_import">
                    <label>Excel File</label>
                    <input type="file" class="form-control" name="excel" required>
                    <div class="row mb-3">
                        <label class="col-sm-6 col-lg-3 d-flex mt-3">
                            <input type="checkbox" class="form-check" name="do-not-charge">
                            <span class="m-1"> Do not charge</span>
                        </label>
                        <label class="col-sm-6 col-lg-3 d-flex mt-3">
                            <input type="checkbox" class="form-check" name="change-expired">
                            <span class="m-1"> Change expired</span>
                        </label>
                        <label class="col-sm-6 col-lg-3 d-flex mt-3">
                            <input type="checkbox" class="form-check" name="only-change-used">
                            <span class="m-1"> Only change used</span>
                        </label>
                    </div>
                </div>
                <button type="submit" class="btn btn-primary w-100">Submit</button>
            </form>
        </div>
        {% if response %}
            <div class="mt-5">
                <h4>Created bills</h4>
                <ul>
                    {% for bill in created_bills %}
                        <li>{{ bill }}</li>
                    {% endfor %}
                </ul>
            </div>
        {% endif %}
        {% if messages %}
            <div class="mt-5">
                <h4>Messages</h4>
                <ul>
                    {% for message in messages %}
                        <li>{{ message }}</li>
                    {% endfor %}
                </ul>
            </div>
        {% endif %}
        {% if exceptions %}
            <div class="mt-5">
                <h4 class="text-danger">Exceptions</h4>
                <ul>
                    {% for d in exceptions %}
                        <li>{{ d }}</li>
                    {% endfor %}
                </ul>
            </div>
        {% endif %}
    </div>
{% endblock %}