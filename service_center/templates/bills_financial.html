{% extends 'base_bootstrap.html' %}
{% load static %}

{% block head_title_tag %}<title>Bill Financial</title>{% endblock %}
{% block body %}
    <script>
        if ( window.history.replaceState ) {
            window.history.replaceState( null, null, window.location.href );
        }
    </script>
    <div class="p-5 font-roboto">
        <h4>Bill Financial</h4>
        <div style="color: lightslategray">
            <form method="POST" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="row form-group mt-2">
                    <div class="col-md-6">
                        <label style="color: midnightblue">Bill Ids</label>
                        <textarea
                            class="form-control" name="bill" placeholder="Bill Ids, ex: 1,2,3,4"
                        >{{ bill }}</textarea>
                        <div class="mt-2">
                            <input type="number" name="b_fin_id">
                            <label for="fin_registry">Bill fin Id</label><br>
                        </div>
                        <div class="mt-2">
                            <input class="form-check-input" type="checkbox" name="b_fin" {% if b_fin %}checked{% endif %}>
                            <label for="fin_registry"> Bill financial registered</label><br>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label style="color: midnightblue">BillItem Ids</label>
                        <textarea
                            class="form-control" name="bill_item" placeholder="BillItem Ids, ex: 1,2,3,4"
                        >{{ bill_item }}</textarea>
                        <div class="mt-2">
                            <input type="number" name="bit_fin_id">
                            <label for="fin_registry">Bill item fin Id</label><br>
                        </div>
                        <div class="mt-2">
                            <input class="form-check-input" type="checkbox" name="bit_fin" {% if bit_fin %}checked{% endif %}>
                            <label for="fin_registry"> BillItem financial registered</label><br>
                        </div>
                    </div>
                </div>
                <div class="row form-group mt-4">
                    <div class="col-md-6">
                        <button type="submit" class="btn btn-primary">Submit</button>
                    </div>
                </div>
            </form>
        </div>
        {% if result %}
        <div class="mt-5">
            <h4>result</h4>
            <p>{{ result }}</p>
        </div>
        {% endif %}
        {% if exceptions %}
        <div class="mt-5">
            <h4 class="text-danger">Exceptions</h4>
            <ul>
            {% for d in exceptions %}
                <li>{{ d }}</li>
            {% endfor %}
            </ul>
        </div>
        {% endif %}
    </div>
{% endblock %}
