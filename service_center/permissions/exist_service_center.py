from core.permissions import BasePermission
from service_center.models import ServiceCenter


class ExistServiceCenter(BasePermission):
    message = ""

    def has_permission(self, request, view):
        try:
            service_center, employee = ServiceCenter.objects.get_by_user(request.user)
            request.service_center = service_center
            request.employee = employee
            return True
        except Exception as e:
            return True
