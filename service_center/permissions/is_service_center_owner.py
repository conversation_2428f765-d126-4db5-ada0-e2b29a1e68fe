from core.permissions import BasePermission
from service_center.models import ServiceCenter


class IsServiceCenterOwner(BasePermission):
    message = "عدم دسترسی! تنها مدیران مراکز ارائه خدمات فعال مجاز هستند."

    def has_permission(self, request, view):
        service_center, employee = ServiceCenter.objects.get_by_user(request.user)
        request.service_center = service_center
        request.employee = employee
        return not employee and service_center and service_center.is_active_state()
