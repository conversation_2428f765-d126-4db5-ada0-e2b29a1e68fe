# Generated by Django 3.2.10 on 2024-08-26 08:15

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('content', '0003_datetranslation'),
        ('service_center', '0014_servicecentershifttranslation_servicetypetranslation'),
    ]

    operations = [
        migrations.AlterField(
            model_name='servicecenterconfig',
            name='fri',
            field=models.ManyToManyField(blank=True, related_name='service_center_config_fri', to='service_center.ServiceCenterShift'),
        ),
        migrations.AlterField(
            model_name='servicecenterconfig',
            name='happy_hour',
            field=models.ManyToManyField(blank=True, related_name='service_center_config_happy', to='service_center.ServiceCenterShift'),
        ),
        migrations.AlterField(
            model_name='servicecenterconfig',
            name='mon',
            field=models.ManyToManyField(blank=True, related_name='service_center_config_mon', to='service_center.ServiceCenterShift'),
        ),
        migrations.AlterField(
            model_name='servicecenterconfig',
            name='off_days',
            field=models.ManyToManyField(blank=True, to='content.Date'),
        ),
        migrations.AlterField(
            model_name='servicecenterconfig',
            name='rush_hour',
            field=models.ManyToManyField(blank=True, related_name='service_center_config_rush', to='service_center.ServiceCenterShift'),
        ),
        migrations.AlterField(
            model_name='servicecenterconfig',
            name='sat',
            field=models.ManyToManyField(blank=True, related_name='service_center_config_sat', to='service_center.ServiceCenterShift'),
        ),
        migrations.AlterField(
            model_name='servicecenterconfig',
            name='service_center',
            field=models.OneToOneField(blank=True, on_delete=django.db.models.deletion.CASCADE, related_name='config', to='service_center.servicecenter'),
        ),
        migrations.AlterField(
            model_name='servicecenterconfig',
            name='sun',
            field=models.ManyToManyField(blank=True, related_name='service_center_config_sun', to='service_center.ServiceCenterShift'),
        ),
        migrations.AlterField(
            model_name='servicecenterconfig',
            name='thu',
            field=models.ManyToManyField(blank=True, related_name='service_center_config_thu', to='service_center.ServiceCenterShift'),
        ),
        migrations.AlterField(
            model_name='servicecenterconfig',
            name='tue',
            field=models.ManyToManyField(blank=True, related_name='service_center_config_tue', to='service_center.ServiceCenterShift'),
        ),
        migrations.AlterField(
            model_name='servicecenterconfig',
            name='wed',
            field=models.ManyToManyField(blank=True, related_name='service_center_config_wed', to='service_center.ServiceCenterShift'),
        ),
    ]
