# Generated by Django 3.2.10 on 2024-08-06 07:33

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('service_center', '0013_bill_billitem'),
    ]

    operations = [
        migrations.CreateModel(
            name='ServiceTypeTranslation',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('lang_code', models.CharField(max_length=35)),
                ('name', models.CharField(blank=True, max_length=250, null=True)),
                ('service_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='translations', to='service_center.servicetype')),
            ],
            options={
                'db_table': 'matrix_service_center_service_type_translation',
                'unique_together': {('lang_code', 'service_type')},
            },
        ),
        migrations.CreateModel(
            name='ServiceCenterShiftTranslation',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('lang_code', models.CharField(max_length=35)),
                ('name', models.CharField(blank=True, max_length=10, null=True)),
                ('service_center_shift', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='translations', to='service_center.servicecentershift')),
            ],
            options={
                'db_table': 'matrix_service_center_service_center_shift_translations',
                'unique_together': {('lang_code', 'service_center_shift')},
            },
        ),
    ]
