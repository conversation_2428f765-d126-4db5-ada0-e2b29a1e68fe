# Generated by Django 3.2.10 on 2025-01-21 13:04

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('service_center', '0015_auto_20240826_0815'),
    ]

    operations = [
        migrations.RenameField(
            model_name='bill',
            old_name='user',
            new_name='customer',
        ),
        migrations.AddField(
            model_name='bill',
            name='verifier',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='verifier_bills', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='servicecenteremployee',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.PROTECT, related_name='employee',
                                       to=settings.AUTH_USER_MODEL, primary_key=False),
        ),
        migrations.AddField(
            model_name='servicecenteremployee',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
            preserve_default=False,
        ),

    ]
