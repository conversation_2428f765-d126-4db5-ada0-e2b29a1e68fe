# Generated by Django 3.2.10 on 2025-04-26 11:01

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('quick_book', '0011_brand'),
        ('service_center', '0016_auto_20250121_1304'),
    ]

    operations = [
        migrations.AddField(
            model_name='servicecenter',
            name='daily_user_visit',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='servicecenter',
            name='engine_oil_cost',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='servicecenter',
            name='engine_oil_service_cost',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='servicecenter',
            name='foreman_first_name',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='servicecenter',
            name='foreman_last_name',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='servicecenter',
            name='foreman_phone_number',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='servicecenter',
            name='needs_stand',
            field=models.BooleanField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='servicecenter',
            name='oil_filter_cost',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='servicecenter',
            name='oil_filter_service_cost',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='servicecenter',
            name='top_seller_brand',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='service_centers', to='quick_book.brand'),
        ),
        migrations.AlterField(
            model_name='servicecenter',
            name='ownership_type',
            field=models.CharField(choices=[('O', 'Owned'), ('R', 'Rental'), ('other', 'Other')], default='R', max_length=16),
        ),
    ]
