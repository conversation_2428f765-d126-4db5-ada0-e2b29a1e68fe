# Generated by Django 3.2.10 on 2023-11-27 08:11

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('service_center', '0002_auto_20231101_1325'),
    ]

    operations = [
        migrations.CreateModel(
            name='ServiceCenterServiceType',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ServiceCenterServiceTypeCapacity',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('capacity', models.PositiveIntegerField(default=10)),
                ('service_center_service_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='capacities', to='service_center.servicecenterservicetype')),
            ],
        ),
        migrations.CreateModel(
            name='ServiceCenterShift',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=10)),
                ('start_time', models.TimeField()),
                ('end_time', models.TimeField()),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.RemoveField(
            model_name='servicecentertype',
            name='service_categories',
        ),
        migrations.RemoveField(
            model_name='servicecentertype',
            name='sites',
        ),
        migrations.RenameField(
            model_name='servicecenter',
            old_name='naghsh_code',
            new_name='commercial_register_code',
        ),
        migrations.RenameField(
            model_name='servicecenter',
            old_name='garage_aria',
            new_name='garage_area',
        ),
        migrations.RenameField(
            model_name='servicecenter',
            old_name='four_column_jack_count',
            new_name='jack_count',
        ),
        migrations.RenameField(
            model_name='servicecenter',
            old_name='store_aria',
            new_name='store_area',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='assistants_count',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='banner_text',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='car_type',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='charge_percentage',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='contract_number',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='diagnostic_station_count',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='driver',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='economical_number',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='financial_representative',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='is_hold',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='is_payment_terminal',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='legal_announcement_date',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='legal_announcement_number',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='license_number',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='location_delivery',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='location_service',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='max_visits',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='max_visits_per_agent',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='mediation_city',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='national_id',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='passenger',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='premium',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='promissory_note_amount',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='promissory_note_number',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='promissory_note_quantity',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='registration_number',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='scissors_jack_count',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='service_center_delivery',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='service_center_service',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='service_center_type',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='sign_contract',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='snappbox_delivery',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='status',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='steering_wheel_jack_count',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='supper_plus',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='tableau_type',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='two_column_jack_count',
        ),
        migrations.RemoveField(
            model_name='servicecenter',
            name='type_code',
        ),
        migrations.RemoveField(
            model_name='servicetype',
            name='type',
        ),
        migrations.AddField(
            model_name='servicecenter',
            name='office_area',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='servicecenter',
            name='types',
            field=models.ManyToManyField(related_name='service_centers', to='service_center.ServiceType'),
        ),
        migrations.AlterField(
            model_name='servicecenter',
            name='visit_status',
            field=models.CharField(choices=[('visit', 'Visit'), ('fast', 'Fast'), ('pend', 'Pending'), ('focus', 'Focus')], default='fast', max_length=100),
        ),
        migrations.AlterField(
            model_name='servicecenterconfig',
            name='max_capacity',
            field=models.PositiveIntegerField(default=100),
        ),
        migrations.DeleteModel(
            name='ServiceCapacity',
        ),
        migrations.DeleteModel(
            name='ServiceCenterType',
        ),
        migrations.AddField(
            model_name='servicecenterservicetypecapacity',
            name='shift',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='service_center_service_type_capacities', to='service_center.servicecentershift'),
        ),
        migrations.AddField(
            model_name='servicecenterservicetype',
            name='service_center',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='service_types', to='service_center.servicecenter'),
        ),
        migrations.AddField(
            model_name='servicecenterservicetype',
            name='service_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='service_center_service_types', to='service_center.servicetype'),
        ),
        migrations.AddField(
            model_name='servicecenterconfig',
            name='fri',
            field=models.ManyToManyField(related_name='service_center_config_fri', to='service_center.ServiceCenterShift'),
        ),
        migrations.AddField(
            model_name='servicecenterconfig',
            name='happy_hour',
            field=models.ManyToManyField(related_name='service_center_config_happy', to='service_center.ServiceCenterShift'),
        ),
        migrations.AddField(
            model_name='servicecenterconfig',
            name='mon',
            field=models.ManyToManyField(related_name='service_center_config_mon', to='service_center.ServiceCenterShift'),
        ),
        migrations.AddField(
            model_name='servicecenterconfig',
            name='rush_hour',
            field=models.ManyToManyField(related_name='service_center_config_rush', to='service_center.ServiceCenterShift'),
        ),
        migrations.AddField(
            model_name='servicecenterconfig',
            name='sat',
            field=models.ManyToManyField(related_name='service_center_config_sat', to='service_center.ServiceCenterShift'),
        ),
        migrations.AddField(
            model_name='servicecenterconfig',
            name='sun',
            field=models.ManyToManyField(related_name='service_center_config_sun', to='service_center.ServiceCenterShift'),
        ),
        migrations.AddField(
            model_name='servicecenterconfig',
            name='thu',
            field=models.ManyToManyField(related_name='service_center_config_thu', to='service_center.ServiceCenterShift'),
        ),
        migrations.AddField(
            model_name='servicecenterconfig',
            name='tue',
            field=models.ManyToManyField(related_name='service_center_config_tue', to='service_center.ServiceCenterShift'),
        ),
        migrations.AddField(
            model_name='servicecenterconfig',
            name='wed',
            field=models.ManyToManyField(related_name='service_center_config_wed', to='service_center.ServiceCenterShift'),
        ),
        migrations.AlterUniqueTogether(
            name='servicecenterservicetypecapacity',
            unique_together={('service_center_service_type', 'shift')},
        ),
    ]
