# Generated by Django 3.2.10 on 2024-07-24 11:01

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('cars', '0002_auto_20240717_0711'),
        ('service_center', '0012_auto_20240721_1317'),
    ]

    operations = [
        migrations.CreateModel(
            name='Bill',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('current_kilometer', models.PositiveIntegerField(default=0)),
                ('next_kilometer', models.PositiveIntegerField(default=0)),
                ('is_coupon', models.BooleanField(default=False, editable=False)),
                ('is_verified', models.BooleanField(default=False, editable=False)),
                ('verified_at', models.DateTimeField(blank=True, editable=False, null=True)),
                ('charge_percentage', models.PositiveIntegerField(default=5, editable=False)),
                ('total_charge_price', models.PositiveIntegerField(default=0, editable=False)),
                ('total_extra_charge', models.PositiveIntegerField(default=0, editable=False)),
                ('charge_amount', models.PositiveIntegerField(default=0, editable=False)),
                ('total_amount', models.PositiveIntegerField(default=0, editable=False)),
                ('service_center', models.ForeignKey(editable=False, on_delete=django.db.models.deletion.PROTECT, related_name='bills', to='service_center.servicecenter')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL)),
                ('user_car', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='bills', to='cars.usercar')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BillItem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('object_id', models.PositiveIntegerField()),
                ('amount', models.PositiveIntegerField(default=0)),
                ('charge_price', models.PositiveIntegerField(default=0)),
                ('state', models.CharField(choices=[('check', 'Check'), ('change', 'Change')], default='change', max_length=10)),
                ('usage_limit', models.PositiveIntegerField(default=0)),
                ('usage_limit_type', models.CharField(choices=[('km', 'کیلومتر'), ('month', 'ماه')], default='km', max_length=20)),
                ('bill', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='service_center.bill')),
                ('content_type', models.ForeignKey(limit_choices_to=models.Q(('model__in', ['orderline', 'serivcecenter'])), on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
