# Generated by Django 3.2.10 on 2023-12-02 12:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('service_center', '0004_alter_servicecenterconfig_service_center'),
    ]

    operations = [
        migrations.RenameField(
            model_name='servicecenter',
            old_name='office_area',
            new_name='administrative_area_size',
        ),
        migrations.RenameField(
            model_name='servicecenter',
            old_name='garage_area',
            new_name='center_size',
        ),
        migrations.RenameField(
            model_name='servicecenter',
            old_name='lobby_area',
            new_name='customer_lounge_size',
        ),
        migrations.RenameField(
            model_name='servicecenter',
            old_name='store_area',
            new_name='inventory_storage_size',
        ),
        migrations.RenameField(
            model_name='servicecenter',
            old_name='jack_count',
            new_name='jac_equipment_count',
        ),
        migrations.RenameField(
            model_name='servicecenter',
            old_name='workstations_count',
            new_name='service_bay_count',
        ),
        migrations.RenameField(
            model_name='servicecenter',
            old_name='width_footage',
            new_name='storefront_area_size',
        ),
        migrations.AlterField(
            model_name='servicecenter',
            name='appearance',
            field=models.CharField(choices=[('A', 'Attractive'), ('AV', 'Average'), ('P', 'Poor')], default='AV', max_length=2),
        ),
    ]
