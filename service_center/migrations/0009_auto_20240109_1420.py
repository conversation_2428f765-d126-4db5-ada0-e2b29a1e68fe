# Generated by Django 3.2.10 on 2024-01-09 14:20

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('locations', '0004_auto_20231225_0902'),
        ('service_center', '0008_auto_20231212_1245'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='servicecenter',
            name='address',
        ),
        migrations.AddField(
            model_name='servicecenter',
            name='addresses',
            field=models.ManyToManyField(related_name='service_centers', to='locations.Address'),
        ),
        migrations.AddField(
            model_name='servicecenter',
            name='default_billing_address',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='service_center_billing_address', to='locations.address'),
        ),
        migrations.AddField(
            model_name='servicecenter',
            name='default_location_address',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='service_center_location_address', to='locations.address'),
        ),
        migrations.AddField(
            model_name='servicecenter',
            name='default_shipping_address',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='service_center_shipping_address', to='locations.address'),
        ),
    ]
