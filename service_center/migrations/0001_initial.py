# Generated by Django 3.2.10 on 2023-09-17 08:58

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('locations', '0001_initial'),
        ('cars', '0001_initial'),
        ('sites', '0002_alter_domain_unique'),
        ('agent', '0002_agentvisit_poll_question'),
        ('sellers', '0001_initial'),
        ('accounts', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('contenttypes', '0002_remove_content_type_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='Bill',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('properties', models.TextField(blank=True, default='[]')),
                ('token', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('current_killometer', models.PositiveIntegerField(default=0)),
                ('is_charged', models.BooleanField(default=False, editable=False)),
                ('avoid_charging', models.BooleanField(default=False)),
                ('avoid_charging_reason', models.CharField(blank=True, choices=[('admin', 'Admin'), ('expired', 'Expired')], max_length=12, null=True)),
                ('is_coupon', models.BooleanField(default=False, editable=False)),
                ('is_credit_charged', models.BooleanField(default=False, editable=False)),
                ('is_verified', models.BooleanField(default=False, editable=False)),
                ('verified_at', models.DateTimeField(blank=True, editable=False, null=True)),
                ('charge_percentage', models.PositiveIntegerField(default=5, editable=False)),
                ('total_charge_price', models.PositiveIntegerField(default=0, editable=False)),
                ('total_extra_charge', models.PositiveIntegerField(default=0, editable=False)),
                ('charge_amount', models.PositiveIntegerField(default=0, editable=False)),
                ('total_amount', models.PositiveIntegerField(default=0, editable=False)),
                ('is_fin_registered', models.BooleanField(default=False, editable=False)),
                ('fin_id', models.PositiveIntegerField(default=0, editable=False)),
                ('fin_date', models.DateTimeField(blank=True, editable=False, null=True)),
                ('ara_fin_date', models.TextField(blank=True, editable=False, max_length=50, null=True)),
                ('car', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='bills', to='cars.usercar')),
            ],
            options={
                'db_table': 'matrix_service_center_bill',
            },
        ),
        migrations.CreateModel(
            name='ServiceCategory',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=250)),
            ],
            options={
                'db_table': 'matrix_service_center_service_category',
            },
        ),
        migrations.CreateModel(
            name='ServiceCenter',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=False, verbose_name='active')),
                ('grade', models.CharField(choices=[('a', 'A'), ('ap', 'A+'), ('b', 'B'), ('bp', 'B+'), ('c', 'C'), ('cp', 'C+')], default='c', max_length=100)),
                ('supper_plus', models.BooleanField(default=False)),
                ('visit_status', models.CharField(choices=[('visit', 'Visit'), ('fast', 'Fast'), ('pend', 'Pending'), ('focus', 'Focus'), ('pri', 'Pri'), ('pre', 'Pre')], default='fast', max_length=100)),
                ('max_visits', models.PositiveIntegerField(default=5)),
                ('max_visits_per_agent', models.PositiveIntegerField(default=5)),
                ('charge_percentage', models.PositiveIntegerField(default=5)),
                ('status', models.CharField(choices=[('normal', 'عادی'), ('cancel_contract', 'لغو قرارداد'), ('has_conflict', 'دارای مغایرت'), ('offender', 'خاطی'), ('reliable', 'مورد اعتماد'), ('lawsuit', 'دعوی حقوقی')], default='normal', max_length=100)),
                ('name', models.CharField(max_length=250)),
                ('phone_number', models.CharField(max_length=250)),
                ('appearance', models.CharField(choices=[('E', 'Excellent'), ('G', 'Good'), ('N', 'Normal'), ('W', 'Weak')], default='G', max_length=1)),
                ('car_type', models.CharField(choices=[('L', 'Local'), ('A', 'Abroad'), ('E', 'Equal')], default='L', max_length=1)),
                ('shelves_count', models.PositiveIntegerField(blank=True, null=True)),
                ('pits_count', models.PositiveIntegerField(blank=True, null=True)),
                ('employees_count', models.PositiveIntegerField(blank=True, null=True)),
                ('assistants_count', models.PositiveIntegerField(blank=True, null=True)),
                ('garage_aria', models.PositiveIntegerField(blank=True, null=True)),
                ('store_aria', models.PositiveIntegerField(blank=True, null=True)),
                ('has_lobby', models.BooleanField(default=False)),
                ('workstations_count', models.PositiveIntegerField(blank=True, null=True)),
                ('four_column_jack_count', models.PositiveIntegerField(blank=True, null=True)),
                ('two_column_jack_count', models.PositiveIntegerField(blank=True, null=True)),
                ('scissors_jack_count', models.PositiveIntegerField(blank=True, null=True)),
                ('steering_wheel_jack_count', models.PositiveIntegerField(blank=True, null=True)),
                ('diagnostic_station_count', models.PositiveIntegerField(blank=True, null=True)),
                ('lobby_area', models.PositiveIntegerField(blank=True, null=True)),
                ('width_footage', models.PositiveIntegerField(blank=True, null=True)),
                ('parking_area', models.PositiveIntegerField(blank=True, null=True)),
                ('building_area', models.PositiveIntegerField(blank=True, null=True)),
                ('ownership_type', models.CharField(choices=[('O', 'Owned'), ('R', 'Rental'), ('N', 'Nothing')], default='R', max_length=1)),
                ('rent_end_date', models.DateTimeField(blank=True, null=True)),
                ('experience_in_year', models.IntegerField(blank=True, null=True)),
                ('license_duration_in_year', models.IntegerField(blank=True, null=True)),
                ('banner_text', models.CharField(blank=True, max_length=250, null=True)),
                ('tableau_type', models.CharField(choices=[('P', 'Personal'), ('C', 'Company'), ('N', 'Nothing')], default='C', max_length=1)),
                ('first_login', models.DateTimeField(blank=True, null=True)),
                ('last_login', models.DateTimeField(blank=True, null=True)),
                ('is_hold', models.BooleanField(default=False)),
                ('service_center_service', models.BooleanField(default=True)),
                ('service_center_delivery', models.BooleanField(default=False)),
                ('location_service', models.BooleanField(default=False)),
                ('location_delivery', models.BooleanField(default=False)),
                ('snappbox_delivery', models.BooleanField(default=False)),
                ('driver', models.BooleanField(default=False)),
                ('passenger', models.BooleanField(default=True)),
                ('premium', models.BooleanField(default=False)),
                ('registration_number', models.CharField(blank=True, max_length=17, null=True)),
                ('national_id', models.CharField(blank=True, max_length=10, null=True)),
                ('economical_number', models.CharField(blank=True, max_length=18, null=True)),
                ('promissory_note_number', models.CharField(blank=True, max_length=69, null=True)),
                ('promissory_note_amount', models.IntegerField(blank=True, null=True)),
                ('promissory_note_quantity', models.IntegerField(blank=True, null=True)),
                ('license_number', models.CharField(blank=True, max_length=10, null=True)),
                ('legal_announcement_number', models.CharField(blank=True, max_length=10, null=True)),
                ('legal_announcement_date', models.DateField(blank=True, null=True)),
                ('service_center_type', models.CharField(blank=True, choices=[('supply', 'تامین'), ('service_center', 'اتوسرویس'), ('commission', 'حق العمل')], max_length=50, null=True)),
                ('type_code', models.CharField(blank=True, choices=[('driver_1', 'DRIVER 1'), ('driver_1_plus', 'DRIVER 1 PLUS'), ('driver_2', 'DRIVER 2'), ('driver_2_plus', 'DRIVER 2 PLUS'), ('driver_3', 'DRIVER 3'), ('driver_3_plus', 'DRIVER 3 PLUS'), ('passenger_1', 'PASSENGER 1'), ('passenger_1_plus', 'PASSENGER 1 PLUS'), ('passenger_2', 'PASSENGER 2'), ('passenger_2_plus', 'PASSENGER 2 PLUS'), ('passenger_3', 'PASSENGER 3'), ('passenger_3_plus', 'PASSENGER 3 PLUS'), ('premium_1', 'PREMIUM 1'), ('premium_1_plus', 'PREMIUM 1 PLUS'), ('premium_2', 'PREMIUM 2'), ('premium_2_plus', 'PREMIUM 2 PLUS'), ('premium_3', 'PREMIUM 3'), ('premium_3_plus', 'PREMIUM 3 PLUS')], max_length=100, null=True)),
                ('sign_contract', models.BooleanField(default=False)),
                ('contract_number', models.CharField(editable=False, max_length=13, null=True)),
                ('naghsh_code', models.CharField(blank=True, max_length=10, null=True)),
                ('is_payment_terminal', models.BooleanField(default=False)),
                ('address', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='service_centers', to='locations.address')),
                ('agent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='service_centers', to='agent.agent')),
                ('fast_agent', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='fast_service_centers', to='agent.agent')),
                ('financial_representative', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='financial_service_centers', to=settings.AUTH_USER_MODEL)),
                ('mediation_city', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='mediation_service_centers', to='locations.city')),
                ('seller', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='service_centers', to='sellers.seller')),
            ],
            options={
                'db_table': 'matrix_service_center_service_center',
            },
        ),
        migrations.CreateModel(
            name='ServiceType',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=250)),
                ('type', models.CharField(choices=[('riding', 'سواری'), ('heavy', 'سنگین'), ('bike', 'موتورسیکلت'), ('pickup_truck', 'وانت بار'), ('mini_truck', 'نیمه سنگین')], default='riding', max_length=20)),
            ],
            options={
                'db_table': 'matrix_service_center_service_type',
            },
        ),
        migrations.CreateModel(
            name='WorkShift',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('cars_per_hour', models.IntegerField(default=10)),
                ('first_shift_start', models.TimeField()),
                ('first_shift_end', models.TimeField()),
                ('second_shift_start', models.TimeField()),
                ('second_shift_end', models.TimeField()),
                ('week_days_first_shift', models.BooleanField(default=False)),
                ('week_days_second_shift', models.BooleanField(default=False)),
                ('first_weekend_first_shift', models.BooleanField(default=False)),
                ('first_weekend_second_shift', models.BooleanField(default=False)),
                ('second_weekend_first_shift', models.BooleanField(default=False)),
                ('second_weekend_second_shift', models.BooleanField(default=False)),
                ('holidays_first_shift', models.BooleanField(default=False)),
                ('holidays_second_shift', models.BooleanField(default=False)),
                ('service_center', models.OneToOneField(on_delete=django.db.models.deletion.PROTECT, related_name='work_shift', to='service_center.servicecenter')),
            ],
            options={
                'db_table': 'matrix_service_center_work_shift',
            },
        ),
        migrations.CreateModel(
            name='Vacation',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('date', models.DateField()),
                ('service_center', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='vacations', to='service_center.servicecenter')),
            ],
            options={
                'db_table': 'matrix_service_center_vacation',
            },
        ),
        migrations.CreateModel(
            name='ServiceCenterType',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=250)),
                ('focus_oil_data', models.BooleanField(default=True)),
                ('focus_filter_data', models.BooleanField(default=True)),
                ('service_categories', models.ManyToManyField(blank=True, related_name='service_center_types', to='service_center.ServiceCategory')),
                ('sites', models.ManyToManyField(blank=True, related_name='service_center_types', to='sites.Site')),
            ],
            options={
                'db_table': 'matrix_service_center_service_center_type',
            },
        ),
        migrations.CreateModel(
            name='ServiceCenterEmployee',
            fields=[
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.PROTECT, primary_key=True, related_name='employee', serialize=False, to='accounts.user')),
                ('service_center', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='employees', to='service_center.servicecenter')),
            ],
            options={
                'db_table': 'matrix_service_center_service_center_employee',
            },
        ),
        migrations.AddField(
            model_name='servicecenter',
            name='service_types',
            field=models.ManyToManyField(blank=True, related_name='service_centers', to='service_center.ServiceType'),
        ),
        migrations.AddField(
            model_name='servicecenter',
            name='sites',
            field=models.ManyToManyField(blank=True, related_name='service_centers', to='sites.Site'),
        ),
        migrations.AddField(
            model_name='servicecenter',
            name='types',
            field=models.ManyToManyField(related_name='service_centers', to='service_center.ServiceCenterType'),
        ),
        migrations.AddField(
            model_name='servicecenter',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.PROTECT, related_name='service_center', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='servicecategory',
            name='part_types',
            field=models.ManyToManyField(blank=True, related_name='part_categories', to='service_center.ServiceType'),
        ),
        migrations.AddField(
            model_name='servicecategory',
            name='service_types',
            field=models.ManyToManyField(blank=True, related_name='service_categories', to='service_center.ServiceType'),
        ),
        migrations.CreateModel(
            name='CarTypePercentage',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('german_cars_percent', models.IntegerField(default=0, validators=[django.core.validators.MaxValueValidator(100), django.core.validators.MinValueValidator(0)])),
                ('japanese_cars_percent', models.IntegerField(default=0, validators=[django.core.validators.MaxValueValidator(100), django.core.validators.MinValueValidator(0)])),
                ('korean_cars_percent', models.IntegerField(default=0, validators=[django.core.validators.MaxValueValidator(100), django.core.validators.MinValueValidator(0)])),
                ('chinese_cars_percent', models.IntegerField(default=0, validators=[django.core.validators.MaxValueValidator(100), django.core.validators.MinValueValidator(0)])),
                ('iranian_cars_percent', models.IntegerField(default=0, validators=[django.core.validators.MaxValueValidator(100), django.core.validators.MinValueValidator(0)])),
                ('service_center', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='car_type_percentages', to='service_center.servicecenter')),
            ],
            options={
                'db_table': 'matrix_service_center_car_type_percentage',
            },
        ),
        migrations.CreateModel(
            name='BillItem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('object_id', models.PositiveIntegerField()),
                ('amount', models.PositiveIntegerField(default=0)),
                ('wage', models.PositiveIntegerField(default=0)),
                ('charge_price', models.PositiveIntegerField(default=0)),
                ('extra_charge', models.PositiveIntegerField(default=0)),
                ('state', models.CharField(choices=[('check', 'Check'), ('change', 'Change')], default='change', max_length=10)),
                ('usage_limit', models.PositiveIntegerField(default=0)),
                ('usage_limit_type', models.CharField(choices=[('km', 'کیلومتر'), ('month', 'ماه')], default='km', max_length=20)),
                ('is_fin_registered', models.BooleanField(default=False, editable=False)),
                ('fin_id', models.PositiveIntegerField(default=0, editable=False)),
                ('fin_date', models.DateTimeField(blank=True, editable=False, null=True)),
                ('ara_fin_date', models.TextField(blank=True, editable=False, max_length=50, null=True)),
                ('metadata', models.JSONField(blank=True, default=dict, null=True)),
                ('bill', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='service_center.bill')),
                ('content_type', models.ForeignKey(limit_choices_to=models.Q(('model__in', ['Product', 'OrderLine', 'ServiceType'])), on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
            ],
            options={
                'db_table': 'matrix_service_center_bill_item',
            },
        ),
        migrations.AddField(
            model_name='bill',
            name='service_center',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='bills', to='service_center.servicecenter'),
        ),
        migrations.AddField(
            model_name='bill',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='bills', to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='Appointment',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('date', models.DateTimeField()),
                ('service_center', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='appointments', to='service_center.servicecenter')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.PROTECT, related_name='appointments', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'matrix_service_center_appointment',
            },
        ),
        migrations.CreateModel(
            name='ServiceCenterServiceCategory',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('part', models.BooleanField(default=False)),
                ('service', models.BooleanField(default=False)),
                ('count', models.PositiveIntegerField(default=0)),
                ('service_category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='service_centers', to='service_center.servicecategory')),
                ('service_center', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='service_categories', to='service_center.servicecenter')),
            ],
            options={
                'db_table': 'matrix_service_center_service_center_service_category',
                'unique_together': {('service_center', 'service_category')},
            },
        ),
    ]
