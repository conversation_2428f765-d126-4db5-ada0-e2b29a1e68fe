# Generated by Django 3.2.10 on 2024-07-21 13:17

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('quick_book', '0005_auto_20240422_0837'),
        ('service_center', '0011_servicecenter_synced_saleor_address'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='billitem',
            name='bill',
        ),
        migrations.RemoveField(
            model_name='billitem',
            name='content_type',
        ),
        migrations.RemoveField(
            model_name='servicecenterconfig',
            name='categories',
        ),
        migrations.AddField(
            model_name='servicetype',
            name='category',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='service_types', to='quick_book.category'),
        ),
        migrations.AddField(
            model_name='servicetype',
            name='type',
            field=models.CharField(choices=[('service', 'service in center'), ('delivery', 'pickup in center')], default='service', max_length=50),
        ),
        migrations.DeleteModel(
            name='Bill',
        ),
        migrations.DeleteModel(
            name='BillItem',
        ),
    ]
