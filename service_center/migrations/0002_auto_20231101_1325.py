# Generated by Django 3.2.10 on 2023-11-01 13:25

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('saleor', '0004_auto_20231025_0757'),
        ('content', '0001_initial'),
        ('service_center', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='servicecenter',
            name='service_types',
        ),
        migrations.CreateModel(
            name='ServiceCenterConfig',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('max_capacity', models.PositiveIntegerField()),
                ('categories', models.ManyToManyField(to='saleor.Category')),
                ('off_days', models.ManyToManyField(to='content.Date')),
                ('service_center', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='service_center.servicecenter')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ServiceCapacity',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('capacity', models.PositiveIntegerField()),
                ('service_center', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='service_capacities', to='service_center.servicecenter')),
                ('service_type', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='service_capacities', to='service_center.servicetype')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
