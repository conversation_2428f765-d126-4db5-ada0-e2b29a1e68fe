from django.urls import include, path
from django.contrib import admin
from .views import export_service_center_view , import_service_center_view

app_name = "service_center"

urlpatterns = [
    path("api/v1/service_center/", include("service_center.api.v1.urls", namespace="v1")),
    path("api/v1/dashboard/", include("service_center.api.dashboard.urls", namespace="dashboard_v1")),
    path("api/v1/app/service-center/", include("service_center.api.app.urls", namespace="app_v1")),
    path(
        "admin/service_center/export/",
        admin.site.admin_view(export_service_center_view),
        name="admin-service-center-export",

    ),

    path(
        "admin/service_center/import/",
        admin.site.admin_view(import_service_center_view),
        name="admin-service-center-import",

    )
]
