services:
  api:
    container_name: ${Environment}-core-django-api-srv 
    image: reg.higaraj.com/fr-core-django/${Environment}-api:$IMAGE_TAG
    build:
      context: .
      dockerfile: Dockerfile
    # ports:
    #   - "8000:8000"
    volumes:
      - core-django-media:/app/media
      - core-django-static:/app/static
      - core-django-logs:/app/var/logs
    depends_on:
      - db
      
    env_file:
      - api.env

    networks:
      core-django:
      public_network:
        aliases:
            - ${Environment}-core-django-srv
  
  db:
    container_name: ${Environment}-core-django-db-srv
    image: reg.higaraj.com/fr-core-django/${Environment}-db:$IMAGE_TAG
    build:
      context: .
      dockerfile: Dockerfile-db

    volumes:
      - core-django-db:/var/lib/postgresql/data
      - core-django-db-config:/etc/postgresql  

    # ports:
    #   - 5432:5432

    env_file:
      - db.env

    networks:
      core-django:
        aliases:
            - ${Environment}-core-django-db-srv

  memcached:
    container_name: ${Environment}-core-django-memcached-srv
    image: reg.higaraj.com/fr-core-django/${Environment}-memcached:$IMAGE_TAG
    build:
      context: .
      dockerfile: Dockerfile-memcached

    # ports:
    #   - "11212:11211"

    networks:
      core-django:
        aliases:
            - ${Environment}-core-django-memcached-srv
  
  rabbitmq:
    container_name: ${Environment}-core-django-rabbitmq-srv
    image: reg.higaraj.com/fr-core-django/${Environment}-rabbitmq:$IMAGE_TAG
    build:
      context: .
      dockerfile: Dockerfile-rabbitmq
    # ports:
      # - "15672:15672"  # Management interface
      # - "5672:5672"    # Default AMQP port
    volumes:
      - core-django-rabbitmq-media:/var/lib/rabbitmq
    networks:
      core-django:
        aliases:
          - ${Environment}-core-django-rabbitmq-srv
    env_file:
      - api.env
  
  worker:
    container_name: ${Environment}-core-django-worker-srv
    image: reg.higaraj.com/fr-core-django/${Environment}-worker:$IMAGE_TAG
    
    build:
      context: .
      dockerfile: Dockerfile
    
    
    volumes:
      - core-django-media:/app/media
      - core-django-static:/app/static
      - core-django-logs:/app/var/logs

    depends_on:
      - db

    env_file:
      - api.env

    restart: unless-stopped
    networks:
      core-django:
        aliases:
            - ${Environment}-core-django-worker-srv
    
    command: celery -A settings.celery_app worker -l INFO -Q periodic_queue


  beat:
    container_name: ${Environment}-higaraj-beat-srv
    image: reg.higaraj.com/fr-core-django/${Environment}-beat:$IMAGE_TAG
    
    build:
      context: .
      dockerfile: Dockerfile

    volumes:
      - core-django-media:/app/media
      - core-django-static:/app/static
      - core-django-logs:/app/var/logs

    depends_on:
      - db
    env_file:
      - api.env
    restart: unless-stopped

    networks:
      core-django:
        aliases:
            - ${Environment}-core-django-beat-srv

    command: celery -A settings.celery_app beat -l INFO


volumes:
  core-django-db:
    name: ${Environment}-core-django-db
    driver: local
  core-django-db-config:
    name: ${Environment}-core-django-db-config
  core-django-rabbitmq-media:
    name: ${Environment}-core-django-rabbitmq-media
    driver: local
  core-django-media:
    name: ${Environment}-core-django-media
    driver: local
  core-django-static:
    name: ${Environment}-core-django-static
    driver: local
  core-django-logs:
    name: ${Environment}-core-django-logs
    driver: local

networks:
  core-django:
    name: ${Environment}-core-django
    driver: bridge
  public_network:
    external: true