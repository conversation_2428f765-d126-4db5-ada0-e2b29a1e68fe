FROM postgres:13

# Install locale packages (example for en_US.UTF-8)
RUN apt-get update && apt-get install -y locales && rm -rf /var/lib/apt/lists/* \
    && localedef -i en_US -c -f UTF-8 -A /usr/share/locale/locale.alias en_US.UTF-8

# Install dependencies and build tools
RUN apt-get update && apt-get install -y \
    postgresql-contrib \
    postgresql-server-dev-13 \
    build-essential \
    git

# Clone and build pglogical
RUN apt-get install -y postgresql-13-pglogical

# Clean up apt cache to reduce image size
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# Add any additional configuration here if needed

CMD ["postgres"]
